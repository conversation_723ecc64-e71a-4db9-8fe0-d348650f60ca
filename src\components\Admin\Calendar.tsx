"use client"
import React, { useMemo, useState, useCallback } from 'react'

// Утилита для форматирования времени в короткий формат HH:MM
function formatShortTime(timeStr?: string): string {
  if (!timeStr) return ''
  const trimmed = timeStr.trim()
  // Если уже в формате HH:MM, возвращаем как есть
  if (/^\d{1,2}:\d{2}$/.test(trimmed)) {
    return trimmed.padStart(5, '0')
  }
  // Если в формате HH:MM:SS, убираем секунды
  const match = trimmed.match(/^(\d{1,2}:\d{2})/)
  if (match) {
    return match[1].padStart(5, '0')
  }
  return trimmed
}

// Компонент для кастомных уведомлений
function Toast({ message, type = 'success', onClose }: { message: string; type?: 'success' | 'error' | 'info'; onClose: () => void }) {
  const [progress, setProgress] = React.useState(100)
  
  const config = {
    success: {
      bg: 'from-emerald-600/90 to-emerald-700/90',
      icon: '✅',
      border: 'border-emerald-500/20',
      progressBg: 'bg-emerald-400'
    },
    error: {
      bg: 'from-red-600/90 to-red-700/90', 
      icon: '❌',
      border: 'border-red-500/20',
      progressBg: 'bg-red-400'
    },
    info: {
      bg: 'from-blue-600/90 to-blue-700/90',
      icon: 'ℹ️',
      border: 'border-blue-500/20',
      progressBg: 'bg-blue-400'
    }
  }

  const { bg, icon, border, progressBg } = config[type]

  React.useEffect(() => {
    const timer = setTimeout(onClose, 4000)
    
    // Анимация прогресс-бара
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev - (100 / 40) // 4000ms / 100ms = 40 шагов
        return newProgress <= 0 ? 0 : newProgress
      })
    }, 100)
    
    return () => {
      clearTimeout(timer)
      clearInterval(interval)
    }
  }, [onClose])

  return (
    <div className="fixed top-4 left-4 right-4 sm:top-6 sm:right-6 sm:left-auto z-[9999] animate-in slide-in-from-right-5 duration-300">
      <div className={`bg-gradient-to-r ${bg} backdrop-blur-md text-white rounded-xl shadow-2xl ${border} border max-w-sm sm:min-w-80 overflow-hidden mx-auto sm:mx-0`}>
        <div className="px-4 py-3 sm:px-6 sm:py-4">
          <div className="flex items-center gap-3">
            <span className="text-lg flex-shrink-0">{icon}</span>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-sm leading-relaxed break-words">{message}</p>
            </div>
              <button 
              onClick={onClose}
              className="text-white/70 hover:text-white transition-colors ml-2 flex-shrink-0 w-7 h-7 flex items-center justify-center rounded-lg hover:bg-white/5 touch-manipulation"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
        {/* Прогресс-бар */}
        <div className="h-1 bg-white/20">
          <div 
            className={`h-full ${progressBg} transition-all duration-100 ease-linear`}
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>
    </div>
  )
}

type SlotInput = { date: string; start_time: string; end_time: string; capacity?: number }
type Slot = SlotInput & { id?: number }
type BlockInput = { date: string; reason?: string }
type Block = BlockInput & { id?: number }
type BreakInput = { date: string; start_time: string; end_time: string; reason?: string }
type Break = BreakInput & { id?: number }

export default function Calendar({ 
  slots = [], 
  blocks = [], 
  breaks = [], 
  onCreateSlot, 
  onCreateBlock, 
  onCreateBreak,
  onRefresh,
  onDeleteBlockParent,
  selected: selectedProp,
  onSelect: onSelectProp
}: { 
  slots?: Slot[]
  blocks?: Block[]
  breaks?: Break[]
  onCreateSlot?: (s: SlotInput) => Promise<Slot | void>
  onCreateBlock?: (b: BlockInput) => Promise<void>
  onCreateBreak?: (br: BreakInput) => Promise<void>
  onRefresh?: () => void
  onDeleteBlockParent?: (id: number) => Promise<void>
  selected?: string | null
  onSelect?: (d: string | null) => void
}) {
  const now = new Date()
  const [viewMonth, setViewMonth] = useState(new Date(now.getFullYear(), now.getMonth(), 1))
  const [internalSelected, setInternalSelected] = useState<string | null>(null)
  const selected = selectedProp !== undefined ? selectedProp : internalSelected
  const setSelected = onSelectProp ? onSelectProp : setInternalSelected
  const [dataLoaded, setDataLoaded] = useState(false)
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'info' } | null>(null)
  const [isSaving, setIsSaving] = useState(false)

  // Проверяем, загружены ли данные (хотя бы один массив не пустой или все пустые но стабильные)
  const hasData = slots.length > 0 || blocks.length > 0 || breaks.length > 0
  
  // Устанавливаем флаг загрузки с небольшой задержкой для плавности, но не во время сохранения
  React.useEffect(() => {
    if (isSaving) return // Не обновляем состояние во время сохранения
    
    const timer = setTimeout(() => {
      setDataLoaded(true)
    }, 100)
    return () => clearTimeout(timer)
  }, [hasData, isSaving])

  // Обеспечиваем, что все данные всегда массивы
  const safeSlots = Array.isArray(slots) ? slots : []
  const safeBlocks = Array.isArray(blocks) ? blocks : []
  const safeBreaks = Array.isArray(breaks) ? breaks : []

  const startDay = useMemo(() => new Date(viewMonth.getFullYear(), viewMonth.getMonth(), 1).getDay(), [viewMonth])
  const daysInMonth = useMemo(() => new Date(viewMonth.getFullYear(), viewMonth.getMonth() + 1, 0).getDate(), [viewMonth])

  // Мемоизированные обработчики для предотвращения лишних перерисовок
  const goToPrevMonth = useCallback(() => {
    setViewMonth(new Date(viewMonth.getFullYear(), viewMonth.getMonth() - 1, 1))
  }, [viewMonth])

  const goToNextMonth = useCallback(() => {
    setViewMonth(new Date(viewMonth.getFullYear(), viewMonth.getMonth() + 1, 1))
  }, [viewMonth])

  const handleDayClick = useCallback((iso: string) => {
    setSelected(iso)
  }, [setSelected])

  const showToast = useCallback((message: string, type: 'success' | 'error' | 'info' = 'success') => {
    console.log('🎯 Showing toast:', message, type) // Для отладки
    setToast({ message, type })
  }, [])

  const hideToast = useCallback(() => {
    setToast(null)
  }, [])

  const pad = (n: number) => n.toString().padStart(2, '0')
  const fmt = (d: Date) => `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())}`

  return (
    <div className={`bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900/20 p-3 sm:p-6 rounded-2xl shadow-2xl border border-gray-700/50 transition-all duration-700 ${dataLoaded && !isSaving ? 'opacity-100 scale-100' : 'opacity-90 scale-[0.98]'} ${isSaving ? 'pointer-events-none' : ''}`}>
      <div className="flex flex-col xl:grid xl:grid-cols-3 gap-4 sm:gap-8">
        <div className="xl:col-span-2">
          <div className="bg-gray-800/50 backdrop-blur-sm p-3 sm:p-6 rounded-xl border border-gray-600/30">
            <div className="flex items-center justify-between mb-4 sm:mb-6">
              <div className="flex items-center gap-2 sm:gap-4">
                <button 
                  onClick={goToPrevMonth} 
                  className="p-2 sm:p-2 bg-gradient-to-r from-purple-600/40 to-pink-600/40 hover:from-purple-700/40 hover:to-pink-700/40 rounded-lg transition-all duration-200 transform hover:scale-105 touch-manipulation"
                >
                  ◀
                </button>
                <div className="text-lg sm:text-xl font-light text-gray-100">
                  {viewMonth.toLocaleString('ru', { month: 'long' })} {viewMonth.getFullYear()}
                </div>
                <button 
                  onClick={goToNextMonth} 
                  className="p-2 sm:p-2 bg-gradient-to-r from-purple-600/40 to-pink-600/40 hover:from-purple-700/40 hover:to-pink-700/40 rounded-lg transition-all duration-200 transform hover:scale-105 touch-manipulation"
                >
                  ▶
                </button>
              </div>
            </div>

            <div className={`grid grid-cols-7 gap-1 sm:gap-2 text-center transition-all duration-500 ${dataLoaded ? 'opacity-100' : 'opacity-70'}`}>
              {['Вс', 'Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб'].map(d => 
                <div key={d} className="text-gray-400 font-medium py-1 sm:py-2 text-xs sm:text-sm">{d}</div>
              )}
              {Array.from({ length: startDay }).map((_, i) => <div key={`pad-${i}`} className="py-2 sm:py-4" />)}
              {Array.from({ length: daysInMonth }).map((_, i) => {
                const day = i + 1
                const d = new Date(viewMonth.getFullYear(), viewMonth.getMonth(), day)
                const iso = fmt(d)
                const isBlocked = safeBlocks.some((b: Block) => b.date === iso)
                const hasSlots = safeSlots.some((s: Slot) => s.date === iso)
                const hasBreaks = safeBreaks.some((br: Break) => br.date === iso)
                const isSelected = selected === iso
                return (
                  <button 
                    key={iso} 
                    onClick={() => handleDayClick(iso)} 
                    className={`
                      relative p-2 sm:p-3 rounded-lg transition-all duration-300 transform hover:scale-105 font-medium text-xs sm:text-sm min-h-[2.5rem] sm:min-h-[3rem] touch-manipulation
                      ${isSelected 
                        ? 'bg-gradient-to-r from-pink-500/40 to-purple-600/40 text-white shadow-lg shadow-pink-500/5' 
                        : 'bg-gray-700/50 hover:bg-gray-600/70 text-gray-200'
                      }
                      ${isBlocked ? 'opacity-50 line-through' : ''}
                      ${!dataLoaded ? 'animate-pulse bg-gray-800/30' : ''}
                    `}
                  >
                    <div className="relative z-10">{day}</div>
                    {hasSlots && (
                      <div className="absolute bottom-0.5 right-0.5 sm:bottom-1 sm:right-1 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-emerald-400 rounded-full shadow-sm animate-pulse"></div>
                    )}
                    {hasBreaks && (
                      <div className="absolute bottom-0.5 left-0.5 sm:bottom-1 sm:left-1 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-orange-400 rounded-full shadow-sm"></div>
                    )}
                    {isBlocked && (
                      <div className="absolute top-0.5 right-0.5 sm:top-1 sm:right-1 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-red-400 rounded-full shadow-sm"></div>
                    )}
                  </button>
                )
              })}
            </div>
          </div>
        </div>

        <div className={`bg-gray-800/50 backdrop-blur-sm p-3 sm:p-6 rounded-xl border border-gray-600/30 transition-all duration-500 ${dataLoaded ? 'opacity-100 translate-y-0' : 'opacity-70 translate-y-2'}`}>
          {!selected && (
            <div className="text-center py-8 sm:py-12">
              <div className={`text-4xl sm:text-6xl mb-4 transition-all duration-700 ${dataLoaded ? 'opacity-20' : 'opacity-10 animate-pulse'}`}>📅</div>
              <div className="text-gray-400 font-light text-sm sm:text-base">Выберите дату в календаре</div>
            </div>
          )}
          {selected && (
            <div className="animate-in slide-in-from-right-5 duration-300">
              <DatePanel 
                date={selected} 
                slots={safeSlots}
                blocks={safeBlocks}
                breaks={safeBreaks} 
                onCreateSlot={onCreateSlot} 
                onCreateBlock={onCreateBlock} 
                onDeleteBlockParent={onDeleteBlockParent}
                onCreateBreak={onCreateBreak}
                onRefresh={onRefresh}
                onShowToast={showToast}
                setIsSaving={setIsSaving}
                onClose={() => setSelected(null)} 
              />
            </div>
          )}
        </div>
      </div>
      
      {/* Уведомления */}
      {toast && (
        <Toast 
          message={toast.message} 
          type={toast.type} 
          onClose={hideToast} 
        />
      )}
    </div>
  )
}

function DatePanel(props: { 
  date: string; 
  slots?: Slot[];
  blocks?: Block[];
  breaks?: Break[]; 
  onCreateSlot?: (s: SlotInput) => Promise<Slot | void>; 
  onCreateBlock?: (b: BlockInput) => Promise<void>; 
  onCreateBreak?: (br: BreakInput) => Promise<void>;
  onDeleteBlockParent?: (id: number) => Promise<void>;
  onShowToast?: (message: string, type?: 'success' | 'error' | 'info') => void;
  setIsSaving?: (saving: boolean) => void;
  onRefresh?: () => void;
  onClose: () => void 
}) {
  const { date, slots = [], blocks = [], breaks: existingBreaks = [], onCreateSlot, onCreateBlock, onDeleteBlockParent, onShowToast, setIsSaving, onRefresh, onClose } = props
  // reference forwarded prop to satisfy TypeScript + ESLint when parent passes it but this panel doesn't use it directly
  void props.onCreateBreak
  // Получаем существующие данные для выбранной даты и мемоизируем
  // чтобы не получать новую ссылку на массив при каждом рендере.
  const existingSlots = useMemo(() => Array.isArray(slots) ? slots.filter(slot => slot.date === date) : [], [slots, date])
  const existingBlocks = useMemo(() => Array.isArray(blocks) ? blocks.filter(block => block.date === date) : [], [blocks, date])
  const existingBreaksForDate = useMemo(() => Array.isArray(existingBreaks) ? existingBreaks.filter(br => br.date === date) : [], [existingBreaks, date])
  // Local optimistic state for blocks (holidays)
  const [localBlocks, setLocalBlocks] = useState<Block[]>(existingBlocks)
  React.useEffect(() => { setLocalBlocks(existingBlocks) }, [existingBlocks, blocks, date])
  const existingHoliday = localBlocks.length > 0 ? localBlocks[0] : null

  const [workStart, setWorkStart] = useState('11:00')
  const [workEnd, setWorkEnd] = useState('17:00')
  const [slotDuration, setSlotDuration] = useState(90)
  const [pauseStart, setPauseStart] = useState('')
  const [pauseEnd, setPauseEnd] = useState('')
  const [workStartErr, setWorkStartErr] = useState(false)
  const [workEndErr, setWorkEndErr] = useState(false)
  const [pauseStartErr, setPauseStartErr] = useState(false)
  const [pauseEndErr, setPauseEndErr] = useState(false)
  const [preview, setPreview] = useState<SlotInput[]>([])
  const [busy, setBusy] = useState(false)
  // Local fields for adding a single slot
  const [singleStart, setSingleStart] = useState('')
  const [singleEnd, setSingleEnd] = useState('')
  const [singleErr, setSingleErr] = useState('')
  // Local optimistic state for slots to provide immediate UI feedback
  const [localSlots, setLocalSlots] = useState<Slot[]>(existingSlots)

  // Sync localSlots when parent props change (e.g., after refresh)
  React.useEffect(() => {
    setLocalSlots(existingSlots)
  }, [existingSlots, slots, date])
  // Local optimistic state for breaks
  const [localBreaks, setLocalBreaks] = useState<Break[]>(existingBreaksForDate)

  React.useEffect(() => {
    setLocalBreaks(existingBreaksForDate)
  }, [existingBreaksForDate, existingBreaks, date])

  function timeToDate(dateIso: string, time: string) {
    return new Date(`${dateIso}T${time}:00`)
  }

  // Форматировать дату в 24-часовой строке HH:MM (убирает AM/PM)
  function formatTime(d: Date) {
    return d.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit', hour12: false })
  }

  // Проверяет и нормализует строку в формате HH:MM (24ч). Возвращает null если некорректно.
  function normalizeToHHMM(input: string): string | null {
    const v = input.trim()
    // patterns: H, HH, HMM, HHMM, H:MM, HH:MM
    if (/^\d{1,2}$/.test(v)) {
      const hh = Number(v)
      if (hh >= 0 && hh <= 23) return `${hh.toString().padStart(2, '0')}:00`
      return null
    }
    if (/^\d{3,4}$/.test(v)) {
      const s = v.padStart(4, '0')
      const hh = Number(s.slice(0, 2))
      const mm = Number(s.slice(2))
      if (hh >= 0 && hh <= 23 && mm >= 0 && mm <= 59) return `${hh.toString().padStart(2, '0')}:${mm.toString().padStart(2, '0')}`
      return null
    }
    if (/^\d{1,2}:\d{1,2}$/.test(v)) {
      const [hhS, mmS] = v.split(':')
      const hh = Number(hhS)
      const mm = Number(mmS)
      if (hh >= 0 && hh <= 23 && mm >= 0 && mm <= 59) return `${hh.toString().padStart(2, '0')}:${mm.toString().padStart(2, '0')}`
      return null
    }
    return null
  }

  function generateSlots() {
    const out: SlotInput[] = []
    if (!workStart || !workEnd || slotDuration <= 0) return out
    
    const workStartTime = timeToDate(date, workStart)
    const workEndTime = timeToDate(date, workEnd)
    if (workStartTime.getTime() >= workEndTime.getTime()) return out

    // Получаем все перерывы для данной даты
    const allBreaks = []
    
    // Перерыв из формы
    if (pauseStart && pauseEnd) {
      allBreaks.push({
        start_time: pauseStart,
        end_time: pauseEnd
      })
    }
    
    // Перерывы из базы данных
    const existingBreaksForDateForGeneration = existingBreaksForDate
    allBreaks.push(...existingBreaksForDateForGeneration)

    console.log('🔍 Generate slots for date:', date)
    console.log('📋 Breaks from form:', pauseStart && pauseEnd ? `${pauseStart}-${pauseEnd}` : 'none')
    console.log('🗄️ Breaks from database:', existingBreaksForDateForGeneration)
    console.log('🔄 All breaks:', allBreaks)

    // Сортируем перерывы по времени начала
    allBreaks.sort((a, b) => a.start_time.localeCompare(b.start_time))

    // Создаем временные интервалы для генерации слотов
    const timeSlots = []
    let currentTime = workStartTime

    for (const breakItem of allBreaks) {
      const breakStart = timeToDate(date, breakItem.start_time)
      const breakEnd = timeToDate(date, breakItem.end_time)
      
      // Добавляем интервал до перерыва
      if (currentTime.getTime() < breakStart.getTime()) {
        timeSlots.push({
          start: new Date(currentTime),
          end: new Date(breakStart)
        })
      }
      
      // Переносим текущее время на окончание перерыва
      if (currentTime.getTime() < breakEnd.getTime()) {
        currentTime = new Date(breakEnd)
      }
    }

    // Добавляем последний интервал после всех перерывов
    if (currentTime.getTime() < workEndTime.getTime()) {
      timeSlots.push({
        start: new Date(currentTime),
        end: new Date(workEndTime)
      })
    }

    console.log('⏰ Time slots for generation:', timeSlots.map(ts => 
      `${formatTime(ts.start)} - ${formatTime(ts.end)}`
    ))

    // Генерируем слоты для каждого временного интервала
    for (const timeSlot of timeSlots) {
      let cur = new Date(timeSlot.start)
      
      while (cur.getTime() + slotDuration * 60000 <= timeSlot.end.getTime()) {
        const next = new Date(cur.getTime() + slotDuration * 60000)
        
        out.push({ 
          date, 
          start_time: formatTime(cur), 
          end_time: formatTime(next), 
          capacity: 1 
        })
        
        console.log('✅ Slot added:', formatTime(cur), '-', formatTime(next))
        cur = next
      }
    }
    
    console.log('🎯 Final slots:', out)
    return out
  }

  function onPreview() { 
    setPreview(generateSlots())
    // Тестовое уведомление
    onShowToast?.('Предпросмотр готов!', 'info')
  }

  async function onSaveSchedule() {
    if (!workStart || !workEnd || slotDuration <= 0) {
      onShowToast?.('Заполните время работы и длительность слота', 'error')
      return
    }
    
    setBusy(true)
    setIsSaving?.(true) // Устанавливаем состояние сохранения
    try {
      const slots = generateSlots()
      
      // Создаем слоты и перерывы параллельно
      const promises = []
      
      // 1. Создаем все слоты параллельно
      if (slots.length > 0) {
        const slotPromises = slots.map(slot => 
          fetch('/api/admin/slots', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(slot)
          })
        )
        promises.push(...slotPromises)
      }
      
      // 2. Создаем перерыв параллельно
      if (pauseStart && pauseEnd) {
        promises.push(
          fetch('/api/admin/breaks', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
              date, 
              start_time: pauseStart, 
              end_time: pauseEnd, 
              reason: 'Перерыв' 
            })
          })
        )
      }
      
      // Выполняем все запросы параллельно
      const results = await Promise.allSettled(promises)
      
      // Проверяем результаты
      let successCount = 0
      let hasErrors = false
      
      for (let i = 0; i < results.length; i++) {
        const result = results[i]
        if (result.status === 'rejected') {
          console.error(`Request ${i} failed:`, result.reason)
          hasErrors = true
        } else if (!result.value.ok) {
          console.error(`Request ${i} returned error:`, result.value.status)
          hasErrors = true
        } else {
          successCount++
        }
      }
      
      if (hasErrors && successCount === 0) {
        throw new Error('Все операции завершились с ошибками')
      }
      
      const message = hasErrors 
        ? `Расписание частично сохранено: ${successCount}/${results.length} операций успешно`
        : `Расписание сохранено: ${slots.length} слотов${pauseStart && pauseEnd ? ' + перерыв' : ''}`
      
      // Показываем уведомление ПЕРЕД закрытием
      onShowToast?.(message, hasErrors ? 'error' : 'success')
      
      // Закрываем панель с небольшой задержкой, чтобы увидеть уведомление
      setTimeout(() => {
        onClose()
      }, 500)
      
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Неизвестная ошибка';
      console.error('Error saving schedule:', error)
      onShowToast?.('Ошибка при сохранении расписания: ' + errorMessage, 'error')
    } finally {
      setBusy(false)
      setIsSaving?.(false) // Сбрасываем состояние сохранения
      setPreview([])
    }
  }

  async function onMarkAsHoliday() {
    // Optimistic add: add a temporary block so UI updates immediately
    const tempId = -(Date.now())
    const tempBlock: Block = { id: tempId, date, reason: 'Выходной (локально)' }
    const prev = localBlocks
    setLocalBlocks(bs => [...bs, tempBlock])
    try {
      if (onCreateBlock) {
        await onCreateBlock({ date })
      }
      onShowToast?.('День отмечен как выходной', 'info')
      // Ask parent to refresh canonical data
      onRefresh?.()
    } catch {
      // revert optimistic addition
      setLocalBlocks(prev)
      onShowToast?.('Ошибка при отмечании выходного дня', 'error')
    }
    onClose()
  }

  // Функции удаления существующих данных
  async function deleteBlockLocal(blockId: number) {
    // Optimistic removal: remove locally first for instant feedback
    const prev = localBlocks
    setLocalBlocks(bs => bs.filter(b => b.id !== blockId))
    try {
      const response = await fetch(`/api/admin/blocks?id=${blockId}`, {
        method: 'DELETE'
      })
      if (response.ok) {
        onShowToast?.('Выходной день удален', 'success')
        // Refresh canonical data in background
        onRefresh?.()
        return
      } else {
        const text = await response.text().catch(() => '')
        throw new Error(text || 'Ошибка удаления')
      }
    } catch (error: unknown) {
      // revert optimistic removal
      setLocalBlocks(prev)
      onShowToast?.('Ошибка при удалении выходного дня', 'error')
      throw error
    }
  }

  async function onDeleteBreak(breakId: number) {
    try {
      const response = await fetch(`/api/admin/breaks?id=${breakId}`, {
        method: 'DELETE'
      })
      if (response.ok) {
        onShowToast?.('Пауза удалена', 'success')
        // Рефреш данных через родительский компонент
        onRefresh?.()
        return
      }

      let serverMessage = ''
      try {
        const json = await response.json()
        serverMessage = (json && (json.error || json.message)) ? (json.error || json.message) : JSON.stringify(json)
      } catch {
        serverMessage = await response.text().catch(() => '')
      }

      onShowToast?.(serverMessage || `Ошибка при удалении паузы (код ${response.status})`, 'error')
      throw new Error(serverMessage || `HTTP ${response.status}`)
    } catch (error: unknown) {
      onShowToast?.('Ошибка при удалении паузы', 'error')
      throw error
    }
  }

  async function onDeleteAllSlots(dateToDelete: string) {
    try {
      const response = await fetch(`/api/admin/slots?date=${dateToDelete}`, {
        method: 'DELETE'
      })
      if (response.ok) {
        onShowToast?.('Все слоты удалены', 'success')
        // Рефреш данных через родительский компонент
        onRefresh?.()
        return
      }

      // Попытаться получить осмысленное сообщение об ошибке из ответа
      let serverMessage = ''
      try {
        const json = await response.json()
        serverMessage = (json && (json.error || json.message)) ? (json.error || json.message) : JSON.stringify(json)
      } catch {
        // Если не JSON, пробуем текст
        serverMessage = await response.text().catch(() => '')
      }

      if (response.status === 409) {
        // Конфликт — показать сообщение сервера или пояснение по-русски
        onShowToast?.(serverMessage || 'Нельзя удалить слоты с активными бронированиями', 'error')
      } else {
        onShowToast?.(serverMessage || `Ошибка при удалении слотов (код ${response.status})`, 'error')
      }
    } catch {
      onShowToast?.('Ошибка при удалении слотов', 'error')
    }
  }

  async function onDeleteSlot(slotId: number) {
    try {
      const response = await fetch(`/api/admin/slots?id=${slotId}`, {
        method: 'DELETE'
      })
      if (response.ok) {
            onShowToast?.('Слот удален', 'success')
            // Рефреш данных через родительский компонент
            onRefresh?.()
            return
      }

      let serverMessage = ''
      try {
        const json = await response.json()
        serverMessage = (json && (json.error || json.message)) ? (json.error || json.message) : JSON.stringify(json)
      } catch {
        serverMessage = await response.text().catch(() => '')
      }

      if (response.status === 409) {
        onShowToast?.(serverMessage || 'Нельзя удалить слот с активными бронированиями', 'error')
      } else {
        onShowToast?.(serverMessage || `Ошибка при удалении слота (код ${response.status})`, 'error')
      }
    } catch (error: unknown) {
          onShowToast?.('Ошибка при удалении слота', 'error')
          throw error
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="text-2xl font-light text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-purple-400 mb-2">
          {new Date(date).toLocaleDateString('ru', { day: 'numeric', month: 'long' })}
        </div>
        <div className="text-sm text-gray-400">
          {new Date(date).toLocaleDateString('ru', { weekday: 'long' })}
        </div>
      </div>

      {/* Existing Data Section */}
      {(existingSlots.length > 0 || existingBreaksForDate.length > 0 || existingHoliday) && (
        <div className="bg-gray-700/20 border border-gray-600/30 rounded-lg p-4 space-y-4">
          <h3 className="text-lg font-medium text-gray-200 mb-3">Текущее расписание</h3>
          
          {existingHoliday && (
            <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full flex-shrink-0"></div>
                  <span className="text-red-200 font-medium">Выходной день</span>
                </div>
                <button 
                  onClick={async () => {
                    if (!existingHoliday?.id) return
                    const id = existingHoliday.id
                    try {
                      if (onDeleteBlockParent) {
                        await onDeleteBlockParent(id)
                      } else {
                        await deleteBlockLocal(id)
                      }
                    } catch {
                      // error already handled in called functions
                    }
                  }}
                  className="text-red-300 hover:text-red-100 transition-colors text-sm touch-manipulation min-h-[2rem] px-2 self-start sm:self-auto"
                >
                  Удалить
                </button>
              </div>
            </div>
          )}

          {localSlots.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-300">Существующие слоты времени ({localSlots.length})</h4>
              <div className="space-y-1 max-h-96 sm:max-h-64 overflow-y-auto">
                {localSlots.map((slot) => (
                  <div key={slot.id} className="bg-green-500/20 border border-green-500/50 rounded px-3 py-2">
                    <div className="flex items-center justify-between">
                      <div className="text-green-200 text-sm">
                        {formatShortTime(slot.start_time)} - {formatShortTime(slot.end_time)}
                      </div>
                      <button 
                        onClick={async () => {
                          if (!slot.id) return
                          const prev = localSlots
                          // optimistic remove
                          setLocalSlots(prevSlots => prevSlots.filter(s => s.id !== slot.id))
                          try {
                            await onDeleteSlot(slot.id)
                            onShowToast?.('Слот удален', 'success')
                            onRefresh?.()
                          } catch {
                            // revert
                            setLocalSlots(prev)
                            onShowToast?.('Ошибка при удалении слота', 'error')
                          }
                        }} 
                        className="text-green-300 hover:text-green-100 transition-colors text-xs touch-manipulation min-h-[2rem] px-2"
                      >
                        Удалить
                      </button>
                    </div>
                  </div>
                ))}
              </div>
              {/* Add single slot UI */}
              <div className="mt-2 bg-gray-700/20 border border-gray-600/30 rounded p-3">
                <div className="text-sm text-gray-300 mb-2">Добавить слот</div>
                <div className="flex flex-col sm:grid sm:grid-cols-2 gap-2">
                  <input value={singleStart} onChange={e => { setSingleStart(e.target.value); setSingleErr('') }} placeholder="HH:MM" className="bg-gray-800/50 border border-gray-600/50 px-3 py-2 rounded min-h-[2.5rem] touch-manipulation" />
                  <input value={singleEnd} onChange={e => { setSingleEnd(e.target.value); setSingleErr('') }} placeholder="HH:MM" className="bg-gray-800/50 border border-gray-600/50 px-3 py-2 rounded min-h-[2.5rem] touch-manipulation" />
                </div>
                {singleErr && <div className="text-xs text-red-400 mt-2">{singleErr}</div>}
                <div className="flex flex-col sm:flex-row gap-2 mt-3">
                  <button onClick={async () => {
                    // Validate
                    const s = normalizeToHHMM(singleStart)
                    const e = normalizeToHHMM(singleEnd)
                    if (!s || !e) { setSingleErr('Неверный формат времени'); return }
                    if (s >= e) { setSingleErr('Время начала должно быть раньше конца'); return }

                    // Optional: check overlap with existing slots (compare in minutes to avoid string/seconds mismatches)
                    function toMinutes(t: string) {
                      // accept HH:MM or HH:MM:SS
                      const parts = t.split(':').map(Number)
                      return (parts[0] || 0) * 60 + (parts[1] || 0)
                    }
                    const sMin = toMinutes(s)
                    const eMin = toMinutes(e)
              const overlap = localSlots.some(sl => {
                      const slStart = toMinutes(sl.start_time)
                      const slEnd = toMinutes(sl.end_time)
                      return !(eMin <= slStart || sMin >= slEnd)
                    })
                    if (overlap) { setSingleErr('Слот пересекается с существующим слотом'); return }

                    if (onCreateSlot) {
                      setBusy(true)
                      const tempId = -(Date.now()) // negative temporary id to avoid collision
                      const tempSlot: Slot = { id: tempId, date, start_time: s, end_time: e, capacity: 1 }
                      // optimistic append
                      setLocalSlots(prev => [...prev, tempSlot])
                      try {
                        const created = await onCreateSlot({ date, start_time: s, end_time: e })
                        onShowToast?.('Слот добавлен', 'success')
                        if (created && (created as Slot).id) {
                          // replace temp slot with real slot
                          setLocalSlots(prev => prev.map(sl => sl.id === tempId ? (created as Slot) : sl))
                        } else {
                          // if parent doesn't return created object, trigger refresh
                          onRefresh?.()
                        }
                      } catch {
                        // revert optimistic add
                        setLocalSlots(prev => prev.filter(sl => sl.id !== tempId))
                        onShowToast?.('Ошибка при добавлении слота', 'error')
                      } finally {
                        setBusy(false)
                        setSingleStart('')
                        setSingleEnd('')
                      }
                    }
                  }} className="bg-emerald-600 hover:bg-emerald-700 px-3 py-2 rounded text-sm disabled:opacity-50 min-h-[2.5rem] touch-manipulation" disabled={busy}>
                    Добавить слот
                  </button>
                  <button onClick={async () => {
                    try {
                      await onDeleteAllSlots(date)
                    } catch {
                      // onDeleteAllSlots already shows toast; nothing else to do here
                    }
                  }} className="text-sm text-red-300 px-3 py-2 rounded border border-red-600/40 min-h-[2.5rem] touch-manipulation">Удалить все слоты</button>
                </div>
              </div>
              
            </div>
          )}

          {localBreaks.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-300">Существующие перерывы ({localBreaks.length})</h4>
              <div className="space-y-1 max-h-48 sm:max-h-64 overflow-y-auto">
                {localBreaks.map((breakItem) => (
                  <div key={breakItem.id} className="bg-yellow-500/20 border border-yellow-500/50 rounded px-3 py-2">
                    <div className="flex items-center justify-between">
                      <div className="text-yellow-200 text-sm min-w-0 flex-1">
                        <div>{formatShortTime(breakItem.start_time)} - {formatShortTime(breakItem.end_time)}</div>
                        {breakItem.reason && <div className="text-yellow-300/70 text-xs mt-1 break-words">({breakItem.reason})</div>}
                      </div>
                      <button 
                        onClick={async () => {
                          if (!breakItem.id) return
                          const prev = localBreaks
                          setLocalBreaks(bs => bs.filter(b => b.id !== breakItem.id))
                          try {
                            await onDeleteBreak(breakItem.id)
                            onShowToast?.('Пауза удалена', 'success')
                            onRefresh?.()
                          } catch {
                            setLocalBreaks(prev)
                            onShowToast?.('Ошибка при удалении паузы', 'error')
                          }
                        }} 
                        className="text-yellow-300 hover:text-yellow-100 transition-colors text-xs ml-2 flex-shrink-0 touch-manipulation min-h-[2rem] px-2"
                      >
                        Удалить
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="space-y-4">
        <div className="bg-gray-700/30 p-3 sm:p-4 rounded-lg border border-gray-600/20">
          <label className="block text-sm font-medium text-gray-300 mb-3">Рабочие часы</label>
          <div className="flex flex-col sm:grid sm:grid-cols-2 gap-3">
            <input
              type="text"
              placeholder="HH:MM"
              value={workStart}
              onChange={(e) => { setWorkStart(e.target.value); setWorkStartErr(false) }}
              onBlur={(e) => {
                const ok = normalizeToHHMM(e.target.value)
                if (ok) setWorkStart(ok)
                else { setWorkStartErr(true) }
              }}
              className={`bg-gray-800/50 border px-3 py-2 rounded-lg transition-all min-h-[2.5rem] touch-manipulation ${workStartErr ? 'border-red-500 ring-2 ring-red-500/30' : 'border-gray-600/50 focus:ring-2 focus:ring-pink-500/50 focus:border-pink-500/50'}`}
            />
            <input
              type="text"
              placeholder="HH:MM"
              value={workEnd}
              onChange={(e) => { setWorkEnd(e.target.value); setWorkEndErr(false) }}
              onBlur={(e) => {
                const ok = normalizeToHHMM(e.target.value)
                if (ok) setWorkEnd(ok)
                else { setWorkEndErr(true) }
              }}
              className={`bg-gray-800/50 border px-3 py-2 rounded-lg transition-all min-h-[2.5rem] touch-manipulation ${workEndErr ? 'border-red-500 ring-2 ring-red-500/30' : 'border-gray-600/50 focus:ring-2 focus:ring-pink-500/50 focus:border-pink-500/50'}`}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4">
          <div className="bg-gray-700/30 p-3 sm:p-4 rounded-lg border border-gray-600/20">
            <label className="block text-sm font-medium text-gray-300 mb-2">Длительность (мин)</label>
            <input 
              type="number" 
              min={5} 
              value={slotDuration} 
              onChange={(e) => setSlotDuration(Number(e.target.value || 0))} 
              className="w-full bg-gray-800/50 border border-gray-600/50 px-3 py-2 rounded-lg focus:ring-2 focus:ring-pink-500/50 focus:border-pink-500/50 transition-all min-h-[2.5rem] touch-manipulation" 
            />
          </div>
        </div>

        <div className="bg-gray-700/30 p-3 sm:p-4 rounded-lg border border-gray-600/20">
          <label className="block text-sm font-medium text-gray-300 mb-3">Пауза (опционально)</label>
          <div className="flex flex-col sm:grid sm:grid-cols-2 gap-3">
            <input
              type="text"
              placeholder="HH:MM"
              value={pauseStart}
              onChange={(e) => { setPauseStart(e.target.value); setPauseStartErr(false) }}
              onBlur={(e) => {
                if (!e.target.value) { setPauseStart(''); return }
                const ok = normalizeToHHMM(e.target.value)
                if (ok) setPauseStart(ok)
                else { setPauseStartErr(true) }
              }}
              className={`bg-gray-800/50 border px-3 py-2 rounded-lg transition-all min-h-[2.5rem] touch-manipulation ${pauseStartErr ? 'border-red-500 ring-2 ring-red-500/30' : 'border-gray-600/50 focus:ring-2 focus:ring-pink-500/50 focus:border-pink-500/50'}`}
            />
            <input
              type="text"
              placeholder="HH:MM"
              value={pauseEnd}
              onChange={(e) => { setPauseEnd(e.target.value); setPauseEndErr(false) }}
              onBlur={(e) => {
                if (!e.target.value) { setPauseEnd(''); return }
                const ok = normalizeToHHMM(e.target.value)
                if (ok) setPauseEnd(ok)
                else { setPauseEndErr(true) }
              }}
              className={`bg-gray-800/50 border px-3 py-2 rounded-lg transition-all min-h-[2.5rem] touch-manipulation ${pauseEndErr ? 'border-red-500 ring-2 ring-red-500/30' : 'border-gray-600/50 focus:ring-2 focus:ring-pink-500/50 focus:border-pink-500/50'}`}
            />
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
              <button 
                onClick={onPreview} 
                className="flex-1 bg-gradient-to-r from-purple-600/40 to-purple-700/40 hover:from-purple-700/40 hover:to-purple-800/40 px-4 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-lg shadow-purple-500/5 min-h-[2.5rem] touch-manipulation"
              >
            Предпросмотр
          </button>
          <button 
            onClick={onSaveSchedule} 
            disabled={busy}
            className="flex-1 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 disabled:opacity-50 disabled:cursor-not-allowed px-4 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-lg shadow-emerald-500/25 min-h-[2.5rem] touch-manipulation"
          >
            {busy ? 'Сохранение...' : 'Сохранить расписание'}
          </button>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          <button 
            onClick={onMarkAsHoliday} 
            className="flex-1 bg-gradient-to-r from-red-600/40 to-red-700/40 hover:from-red-700/40 hover:to-red-800/40 px-4 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-lg shadow-red-500/5 min-h-[2.5rem] touch-manipulation"
          >
            Выходной день
          </button>
          <button 
            onClick={onClose} 
            className="flex-1 bg-gray-700 hover:bg-gray-600 px-4 py-3 rounded-lg font-medium transition-all duration-200 min-h-[2.5rem] touch-manipulation"
          >
            Закрыть
          </button>
        </div>

        {preview.length > 0 && (
          <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 p-3 sm:p-4 rounded-lg border border-gray-600/30 backdrop-blur-sm">
            <div className="text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
              <span className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse flex-shrink-0"></span>
              <span>Предпросмотр слотов ({preview.length})</span>
            </div>
            <div className="space-y-2 max-h-32 sm:max-h-40 overflow-y-auto">
              {preview.map((p, idx) => (
                <div key={`${p.date}-${p.start_time}-${idx}`} className="flex justify-between items-center bg-gray-800/50 p-2 sm:p-3 rounded-lg border border-gray-600/20">
                  <div className="font-mono text-xs sm:text-sm">{formatShortTime(p.start_time)} — {formatShortTime(p.end_time)}</div>
                  <div className="text-xs text-gray-400 flex-shrink-0 ml-2">×{p.capacity}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Показываем существующие перерывы для данной даты */}
        {existingBreaks.filter((br: Break) => br.date === date).length > 0 && (
          <div className="bg-gradient-to-r from-orange-800/50 to-orange-700/50 p-3 sm:p-4 rounded-lg border border-orange-600/30 backdrop-blur-sm">
            <div className="text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
              <span className="w-2 h-2 bg-orange-400 rounded-full flex-shrink-0"></span>
              <span>Существующие перерывы</span>
            </div>
            <div className="space-y-2 max-h-32 sm:max-h-40 overflow-y-auto">
              {existingBreaks.filter((br: Break) => br.date === date).map((br: Break, idx: number) => (
                <div key={`${br.date}-${br.start_time}-${idx}`} className="flex justify-between items-center bg-orange-800/50 p-2 sm:p-3 rounded-lg border border-orange-600/20">
                  <div className="font-mono text-xs sm:text-sm min-w-0 flex-1">
                    <div>{formatShortTime(br.start_time)} — {formatShortTime(br.end_time)}</div>
                    {br.reason && <div className="text-orange-300/70 text-xs mt-1 break-words">{br.reason}</div>}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

  {/* Причина выходного удалена — отправляем только дату при отметке выходного */}
      </div>
    </div>
  )
}
