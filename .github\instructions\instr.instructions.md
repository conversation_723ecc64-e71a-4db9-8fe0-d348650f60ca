---
applyTo: '**'
---
Provide project context and coding guidelines that AI should follow when generating code, answering questions, or reviewing changes.---
applyTo: '**'
---
придерживатся clean code, best practices, DRY, KISS, SOLID, YAGNI, и других принципов разработки ПО.избегать дублирования кода и следовать принципу единственной ответственности.
избегать избыточности и сложных решений, если они не оправданы. использовать понятные и читаемые имена переменных, функций и классов. писать код, который легко поддерживать и расширять в будущем. избегать излишней оптимизации на ранних этапах разработки.
использовать комментарии только там, где это действительно необходимо для понимания кода. след
овать принципу "читаемость важнее краткости" - код должен быть понятным и легким для чтения, даже если это требует немного больше строк кода.
избегать использования магических чисел и строк, использовать константы и перечисления для улучшения читаемости. следовать принципу "один уровень абстракции в одном методе" - методы должны быть достаточно короткими и понятными, чтобы их можно было легко понять без необходимости углубляться в детали.
избегать излишней вложенности и сложных конструкций, использовать простые и понятные конструкции языка. следовать принципу "не усложняй" - избегать излишней сложности и многословности в коде, если это не оправдано.
избегать использования устаревших или неэффективных методов и подходов, использовать современные и эффективные решения. следовать принципу "тестируемость" - писать код, который легко тестировать и поддерживать, избегать сложных зависимостей и побочных эффектов.
говорить со мной по русски
в коде использавать только английский язык, в том числе в названиях переменных, функций и классов. избегать использования русских слов и фраз в коде.
для юзера выводить сообщения на латышском и русском языках, в зависимости от версии языка интерфейса, но в коде использовать только английский язык.
избегать использования устаревших или неэффективных методов и подходов, использовать современные и эффективные решения. следовать принципу "тестируемость" - писать код, который легко тестировать и поддерживать, избегать сложных зависимостей и побочных эффектов.
учитывать что проект на двух языках! Латышский и Русский
Tailwind first
не нужно создавать никаких инструкций для пользавателя
не создавать никаких отчетов,инструкций 
никаких файлов md, все писать тольков в чате
не запускать сервер не создавать файлы конфигурации, не создавать файлы документации.
пиши комментарии к коду, но не создавать отдельные файлы с инструкциями или документацией.
пиши комментарии только в действительно необходимых местах, чтобы не загромождать код излишними пояснениями.
не писать в коде existing code и это тоже нет   // ...  Не засоряй код
помнить про Лучший пользовательский опыт
админ панель только на русском