import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: '#1a1625',
        foreground: '#f8f7ff',
        primary: '#d9d9dd',
        secondary: '#cfcfd3',
        accent: '#e6e6e8',
        muted: '#2d253a',
        border: '#3d3446',
        purple: {
          50: '#fafafa',
          100: '#f5f5f6',
          200: '#ebebec',
          300: '#e0e0e1',
          400: '#d6d6d8',
          500: '#cfcfd3',
          600: '#c6c6ca',
          700: '#bdbdc1',
          800: '#b4b4b8',
          900: '#a9a9ad',
        },
        pink: {
          50: '#fafafa',
          100: '#f5f5f6',
          200: '#ebebec',
          300: '#e0e0e1',
          400: '#d6d6d8',
          500: '#cfcfd3',
          600: '#c6c6ca',
          700: '#bdbdc1',
          800: '#b4b4b8',
          900: '#a9a9ad',
        },
      },
      fontFamily: {
        sans: ['var(--font-inter)', 'Inter', 'sans-serif'],
        serif: ['var(--font-playfair)', 'Playfair Display', 'serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.6s ease-in-out',
        'slide-up': 'slideUp 0.6s ease-out',
        'scale-in': 'scaleIn 0.4s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
  ],
};

export default config;
