import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_ROLE = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE) {
      console.error('Supabase env vars are missing: SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY')
      return res.status(500).json({ error: 'Supabase is not configured on the server' })
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE)

    if (req.method === 'GET') {
      const { date } = req.query

      let query = supabase
        .from('breaks')
        .select('*')
        .order('date', { ascending: true })
        .order('start_time', { ascending: true })

      // Если указана дата, фильтруем по ней
      if (date && typeof date === 'string') {
        query = query.eq('date', date)
      }

      const { data, error } = await query

      if (error) {
        console.error('Supabase select breaks error:', error)
        return res.status(500).json({ error: 'Failed to fetch breaks' })
      }

      return res.status(200).json(data || [])
    }

    if (req.method === 'POST') {
      const { date, start_time, end_time, reason = 'Перерыв', created_by = 'admin' } = req.body
      
      if (!date || !start_time || !end_time) {
        return res.status(400).json({ error: 'Missing required fields: date, start_time, end_time' })
      }

      if (start_time >= end_time) {
        return res.status(400).json({ error: 'Start time must be before end time' })
      }

      // Проверяем, нет ли пересекающихся пауз на ту же дату
      const { data: existingBreaks, error: checkError } = await supabase
        .from('breaks')
        .select('*')
        .eq('date', date)
        .or(`and(start_time.lte.${end_time},end_time.gt.${start_time})`)

      if (checkError) {
        console.error('Supabase check breaks error:', checkError)
        return res.status(500).json({ error: 'Failed to check existing breaks' })
      }

      if (existingBreaks && existingBreaks.length > 0) {
        return res.status(409).json({ error: 'Break time conflicts with existing break' })
      }

      const { data, error } = await supabase
        .from('breaks')
        .insert([{ date, start_time, end_time, reason, created_by }])
        .select()
        .single()

      if (error) {
        console.error('Supabase insert break error:', error)
        return res.status(500).json({ error: 'Failed to create break' })
      }

      return res.status(201).json(data)
    }

    if (req.method === 'DELETE') {
      const { id } = req.query
      
      if (!id || isNaN(Number(id))) {
        return res.status(400).json({ error: 'Invalid ID' })
      }

      const { error } = await supabase
        .from('breaks')
        .delete()
        .eq('id', Number(id))

      if (error) {
        console.error('Supabase delete break error:', error)
        return res.status(500).json({ error: 'Failed to delete break' })
      }

      return res.status(200).json({ success: true })
    }

    return res.status(405).json({ error: 'Method not allowed' })
  } catch (error) {
    console.error('Unexpected error in breaks API:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
