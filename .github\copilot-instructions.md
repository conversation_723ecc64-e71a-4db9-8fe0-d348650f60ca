<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This is a Next.js project for a cosmetologist website called "About your skin". Please follow these design and functionality guidelines:

## Design Requirements
- Modern, elegant, minimalistic design with dark pastel tones
- Animated splash screen with logo 'About your skin' (letters appear one by one, soft fade-in, outline/glow effect)
- Stylish, readable font (use Google Fonts)
- Custom cursor for premium feel
- Fully responsive (mobile + desktop)
- Premium aesthetics and professional trust

## Language Support
- Latvian as default language
- Russian as secondary language
- Language switcher component

## Sections
1. Animated splash screen
2. Hero section with logo and CTA
3. About the cosmetologist
4. Services offered
5. Before/After gallery
6. Testimonials
7. Booking form (calendar, time slots, email notification)

## Technical Guidelines
- Use TypeScript
- Use Tailwind CSS for styling
- Use Framer Motion for animations
- Use placeholders for content
- Focus on smooth animations and transitions
- Dark pastel color scheme
