'use client';

import { useState, useEffect } from 'react';
import SplashScreen from '@/components/SplashScreen';
import Navigation from '@/components/Navigation';
import HeroSection from '@/components/HeroSection';
import AboutSection from '@/components/AboutSection';
import ServicesSection from '@/components/ServicesSection';
import GallerySection from '@/components/GallerySection';
import BookingSection from '@/components/BookingSection';
import Footer from '@/components/Footer';

export default function Home() {
  const [showSplash, setShowSplash] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  if (!isMounted) {
    return null;
  }

  return (
    <>
      {showSplash && <SplashScreen onComplete={handleSplashComplete} />}
      
      {!showSplash && (
        <>
          <Navigation />
          <main>
            <HeroSection />
            <AboutSection />
            <ServicesSection />
            <GallerySection />
            <BookingSection />
          </main>
          <Footer />
        </>
      )}
    </>
  );
}
