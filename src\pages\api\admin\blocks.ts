import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_ROLE = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE) {
      console.error('Supabase env vars are missing: SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY')
      return res.status(500).json({ error: 'Supabase is not configured on the server' })
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE)

    if (req.method === 'GET') {
      const { data, error } = await supabase
        .from('blocks')
        .select('*')
        .order('date', { ascending: true })

      if (error) {
        console.error('Supabase select blocks error:', error)
        return res.status(500).json({ error: 'Failed to fetch blocks' })
      }

      return res.status(200).json(data || [])
    }

    if (req.method === 'POST') {
      const { date, reason, created_by = 'admin' } = req.body
      
      if (!date) {
        return res.status(400).json({ error: 'Missing required field: date' })
      }

      // Проверяем, нет ли уже блокировки на эту дату
      const { data: existingBlock, error: checkError } = await supabase
        .from('blocks')
        .select('id')
        .eq('date', date)
        .maybeSingle()

      if (checkError) {
        console.error('Supabase check blocks error:', checkError)
        return res.status(500).json({ error: 'Failed to check existing blocks' })
      }

      if (existingBlock) {
        return res.status(409).json({ error: 'Date is already blocked' })
      }

      const { data, error } = await supabase
        .from('blocks')
        .insert([{ date, reason: reason || null, created_by }])
        .select()
        .single()

      if (error) {
        console.error('Supabase insert block error:', error)
        return res.status(500).json({ error: 'Failed to create block' })
      }

      return res.status(201).json(data)
    }

    if (req.method === 'DELETE') {
      const { id } = req.query
      
      if (!id || isNaN(Number(id))) {
        return res.status(400).json({ error: 'Invalid ID' })
      }

      const { error } = await supabase
        .from('blocks')
        .delete()
        .eq('id', Number(id))

      if (error) {
        console.error('Supabase delete block error:', error)
        return res.status(500).json({ error: 'Failed to delete block' })
      }

      return res.status(200).json({ success: true })
    }

    return res.status(405).json({ error: 'Method not allowed' })
  } catch (error) {
    console.error('Unexpected error in blocks API:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
