import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_ROLE = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE) {
      console.error('Supabase env vars are missing')
      return res.status(500).json({ error: 'Supabase not configured' })
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE)
    const { id } = req.query

    if (!id || isNaN(Number(id))) {
      return res.status(400).json({ error: 'Invalid booking ID' })
    }

    if (req.method === 'PATCH') {
      const { status } = req.body
      
      if (!status || !['booked', 'cancelled', 'completed'].includes(status)) {
        return res.status(400).json({ error: 'Invalid status' })
      }

      const { data, error } = await supabase
        .from('bookings')
        .update({ status })
        .eq('id', Number(id))
        .select()
        .single()

      if (error) {
        console.error('Supabase update error', error)
        return res.status(500).json({ error: 'Failed to update booking' })
      }

      return res.status(200).json(data)
    }

    if (req.method === 'DELETE') {
      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', Number(id))

      if (error) {
        console.error('Supabase delete error', error)
        return res.status(500).json({ error: 'Failed to delete booking' })
      }

      return res.status(200).json({ success: true })
    }

    return res.status(405).json({ error: 'Method not allowed' })
  } catch (err) {
    console.error('Unexpected error in booking [id] API:', err)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
