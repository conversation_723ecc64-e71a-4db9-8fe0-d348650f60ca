import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { buildReminderSMS } from '@/lib/notifications';
import { sendSMSViaTwilio } from '@/lib/twilio';

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE) {
  console.error('Supabase env vars are missing for send-reminders');
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') return res.status(405).json({ error: 'Method not allowed' });
  if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE) return res.status(500).json({ error: 'Supabase not configured' });

  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE);

  try {
    // Find upcoming bookings within next 24 hours that have not received reminder
    const now = new Date();
    const in24 = new Date(now.getTime() + 24 * 60 * 60 * 1000);

    const startIso = now.toISOString().split('T')[0];
    const endIso = in24.toISOString().split('T')[0];

    // Simple query: get bookings with date between today and in24 and reminder_sent = false
    const { data: bookings, error } = await supabase
      .from('bookings')
      .select('*')
      .gte('date', startIso)
      .lte('date', endIso)
      .eq('status', 'booked')
      .eq('reminder_sent', false);

    if (error) {
      console.error('Supabase fetch reminders error', error);
      return res.status(500).json({ error: error.message });
    }

    if (!Array.isArray(bookings) || bookings.length === 0) {
      return res.status(200).json({ sent: 0 });
    }

    let sent = 0;
    for (const b of bookings) {
      try {
        const body = buildReminderSMS({
          name: b.name,
          date: b.date,
          time: b.time,
          address: process.env.BUSINESS_ADDRESS || undefined,
          language: b.language || 'ru',
        });

        const smsSuccess = await sendSMSViaTwilio({
          to: b.phone,
          text: body
        });

        if (smsSuccess) {
          // mark reminder_sent = true
          await supabase.from('bookings').update({ reminder_sent: true }).eq('id', b.id);
          sent++;
        }
      } catch (e) {
        console.error('Failed sending reminder for booking', b.id, e);
        // continue
      }
    }

    return res.status(200).json({ sent });
  } catch (err: unknown) {
    const message = err instanceof Error ? err.message : String(err);
    console.error('send-reminders error', message);
    return res.status(500).json({ error: message });
  }
}
