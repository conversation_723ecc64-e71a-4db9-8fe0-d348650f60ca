'use client';

import { motion } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';
import { Sparkles, Search, Clock, Zap } from 'lucide-react';

export default function ServicesSection() {
  const { t } = useLanguage();

  const services = [
    {
      icon: Sparkles,
      title: t('facialCleaning'),
      description: t('facialCleaningDesc'),
      price: '80-120€',
      duration: '90 min',
      features: [t('featureDeepCleaning'), t('featureFaceMassage'), t('featureHydration')],
    },
    {
      icon: Zap,
      title: t('antiAging'),
      description: t('antiAgingDesc'),
      price: '150-250€',
      duration: '120 min',
      features: [t('featureRF'), t('featureMesotherapy'), t('featureLED')],
    },
    {
      icon: Search,
      title: t('skinAnalysis'),
      description: t('skinAnalysisDesc'),
      price: t('freeText'),
      duration: '30 min',
      features: [t('featureSkinAnalysis'), t('featureConsultation'), t('featureRecommendations')],
    },
  ];

  return (
    <section id="services" className="py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#dbeafe33' }}>
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            className="inline-block px-4 py-2 bg-accent/10 border border-accent/20 rounded-full text-accent text-sm font-medium mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            {t('services')}
          </motion.div>

          <motion.h2
            className="text-4xl md:text-5xl font-serif text-foreground mb-6 leading-tight"
            style={{ fontFamily: "'Playfair Display', serif" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            {t('servicesHeading')}
          </motion.h2>

          <motion.p
            className="text-lg text-foreground/80 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            {t('servicesSubtitle')}
          </motion.p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const Icon = service.icon;
            return (
              <motion.div
                key={index}
                className="group relative bg-gradient-to-br from-background to-muted/30 p-8 rounded-3xl border border-border/50 backdrop-blur-sm hover:border-accent/50 transition-all duration-500"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                whileHover={{ y: -10, scale: 1.02 }}
              >
                {/* Service Icon */}
                <motion.div
                  className="w-16 h-16 bg-accent/10 rounded-2xl flex items-center justify-center mb-6 group-hover:bg-accent/20 transition-colors duration-300"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <Icon className="w-8 h-8 text-accent" />
                </motion.div>

                {/* Service Info */}
                <h3 className="text-xl font-semibold text-foreground mb-3 group-hover:text-accent transition-colors duration-300">
                  {service.title}
                </h3>
                
                <p className="text-foreground/70 mb-6 leading-relaxed">
                  {service.description}
                </p>

                {/* Features */}
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-foreground/80">
                      <div className="w-1.5 h-1.5 bg-accent rounded-full mr-3" />
                      {feature}
                    </li>
                  ))}
                </ul>

                {/* Price and Duration */}
                <div className="flex items-center justify-between mb-6 pt-4 border-t border-border/30">
                  <div className="text-2xl font-bold text-accent">
                    {service.price}
                  </div>
                  <div className="flex items-center text-foreground/60 text-sm">
                    <Clock className="w-4 h-4 mr-1" />
                    {service.duration}
                  </div>
                </div>

                {/* CTA Button */}
                <motion.button
                  className="w-full bg-accent/70 hover:bg-accent/60 hover:text-background text-accent py-3 rounded-xl font-medium transition-all duration-300 border border-accent/20 hover:border-accent"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => document.querySelector('#booking')?.scrollIntoView({ behavior: 'smooth' })}
                >
                  {t('reserveButton')}
                </motion.button>

                {/* Hover Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-accent/5 to-secondary/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
              </motion.div>
            );
          })}
        </div>

        {/* CTA Section */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <p className="text-foreground/70 mb-6">
            {t('chooseServiceCTA')}
          </p>
          <motion.button
            className="bg-accent/70 text-background px-8 py-4 rounded-full font-medium hover:bg-accent/60 transition-all duration-300 shadow-lg hover:shadow-accent/5"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => document.querySelector('#booking')?.scrollIntoView({ behavior: 'smooth' })}
          >
            {t('bookNow')}
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
