'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

type Language = 'lv' | 'ru';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const translations = {
  lv: {
  // Navigation
  about: 'Par mani',
  services: 'Pakalpojumi',
  gallery: 'Galerija',
  booking: 'Pieteikties vizītei',
    
    // Hero Section
    heroTitle: 'Profesionāla sejas kopšana Rīgā',
    heroSubtitle: 'Atklājiet skaistumu ar mūsu ekskluzīvajiem kosmetoloģijas pakalpojumiem',
    heroButton: 'Rezervēt vizīti',
  splashSubtitle: 'KOSMETOLOĢE • RĪGA',
    
    // About Section
    aboutTitle: 'Par mani',
  aboutText: 'Sertificēta kosmetoloģe ar vairāk nekā 10 gadu pieredzi. Specializējos modernās sejas kopšanas metodēs un individuālā pieejā katram klientam.',
  aboutShortTitle: 'Profesionāla kosmetoloģe',
  withExperience: 'ar pieredzi',
    viewServices: 'Aps<PERSON><PERSON>t pakalpojumus',
  // Follow Us CTA
  followUs: 'Seko man',
  followUsDesc: 'Sekojiet man Instagram un Facebook, lai redzētu jaunākos rezultātus un piedāvājumus.',
    
    // Services Section
    servicesTitle: 'Pakalpojumi',
    facialCleaning: 'Sejas dziļā tīrīšana',
    facialCleaningDesc: 'Profesionāla sejas tīrīšana ar mūsdienīgām metodēm',
    antiAging: 'Pretnovecošanās procedūras',
    antiAgingDesc: 'Efektīvas procedūras ādas jaunības saglabāšanai',
    skinAnalysis: 'Ādas analīze',
    skinAnalysisDesc: 'Detalizēta ādas stāvokļa analīze un ieteikumi',
  servicesHeading: 'Mani pakalpojumi',
  servicesSubtitle: 'Individuāls pieejums katram klientam ar mūsdienīgākajām metodēm',
  featureDeepCleaning: 'Dziļā tīrīšana',
  featureFaceMassage: 'Sejas masāža',
  featureHydration: 'Mitrināšana',
  featureRF: 'RF lifting',
  featureMesotherapy: 'Mezoterapija',
  featureLED: 'LED terapija',
  featureSkinAnalysis: 'Ādas analīze',
  featureConsultation: 'Konsultācija',
  featureRecommendations: 'Ieteikumi',
  reserveButton: 'Rezervēt',
  bookNow: 'Rezervēt',
  freeText: 'Bez maksas',
  chooseServiceCTA: 'Izvēlieties pakalpojumu un rezervējiet ērtā laikā.',
    
    // Gallery Section
    galleryTitle: 'Pirms/Pēc',
      gallerySubtitle: 'Rezultāti, kas runā paši par sevi',
      galleryDesc1: 'Sejas dziļā tīrīšana - 3 mēnešu rezultāts',
      galleryDesc2: 'RF lifting procedūras - 6 nedēļu rezultāts',
      galleryDesc3: 'Mezoterapija - 2 mēnešu rezultāts',
      galleryDesc4: 'LED terapija - 4 nedēļu rezultāts',
      before: 'Pirms',
      after: 'Pēc',
      galleryDisclaimer: '* Rezultāti var atšķirties atkarībā no individuālajām īpašībām',
      galleryCta: 'Gatavi sākt savu skaistuma ceļojumu?',
      bookConsultation: 'Rezervēt konsultāciju',
    
  // Testimonials Section (removed)
    
  // Booking Section
  bookingTitle: 'Rezervēt vizīti',
  bookingSubtitle: 'Izvēlieties sev ērtāko laiku',
  noAvailableSlots: 'Nav pieejamu laiku',
  name: 'Vārds',
  surname: 'Uzvārds',
  email: 'E-pasts',
  phone: 'Telefons',
  service: 'Pakalpojums',
  date: 'Datums',
  time: 'Laiks',
  message: 'Ziņojums',
  submit: 'Nosūtīt pieteikumu',
  visitType: 'Vai tas ir pirmais apmeklējums?',
  visitTypeFirst: 'Pirmais apmeklējums',
  visitTypeRepeat: 'Atkārtots apmeklējums',
  acceptRules: 'Esmu iepazinies ar rezervācijas noteikumiem',
    
    // Footer
  contact: 'Kontakti',
  address: 'Adrese: Rīga, Latvija',
  phone_number: 'Telefons: +371 26094018',
  email_address: 'E-pasts: <EMAIL>',
  selectOption: 'Izvēlieties...',
  addressLabel: 'Adrese',
  addressValue: 'Bruņinieku iela 27, Rīga',
  phoneLabel: 'Telefons',
  phoneValue: '+37126094018',
  emailLabel: 'E-pasts',
  emailValue: '<EMAIL>',
  cityName: 'Rīgā',
  brandDesc: 'Profesionāla kosmetoloģija Rīgā. Mūsu mērķis ir palīdzēt jums atklāt skaistumu un justies pārliecināti savā ādā.',
  quickLinksTitle: 'Ātrās saites',
  copyrightText: '© 2024 About your skin. Visas tiesības aizsargātas.',
  developedWith: 'Izstrādāts ar',
  close: 'Aizvērt',
  thankYou: 'Paldies! Mēs sazināsimies ar jums tuvākajā laikā.',
  messagePlaceholder: 'Papildu informācija vai jautājumi...',
  callUs: 'Jautājumu gadījumā:',
  visitTypeRequired: 'Lūdzu, norādiet vai tas ir pirmais apmeklējums vai atkārtots',
  fillRequired: 'Lūdzu, aizpildiet visus obligātos laukus',
  bookingSuccess: 'Paldies par jūsu rezervāciju!',
  acceptRulesRequired: 'Lūdzu, atzīmējiet, ka esat iepazinies ar rezervācijas noteikumiem',
  dateFullyBooked: 'Visi laiki šajā datumā ir aizņemti.',
  },
  ru: {
  // Navigation
  about: 'Обо мне',
  services: 'Услуги',
  gallery: 'Галерея',
  booking: 'Запись',
    
    // Hero Section
    heroTitle: 'Профессиональный уход за лицом в Риге',
    heroSubtitle: 'Откройте красоту с нашими эксклюзивными косметологическими услугами',
    heroButton: 'Записаться на приём',
  splashSubtitle: 'КОСМЕТОЛОГ • РИГА',
    
    // About Section
    aboutTitle: 'Обо мне',
  aboutText: 'Сертифицированный косметолог с более чем 10-летним опытом. Специализируюсь на современных методах ухода за лицом и индивидуальном подходе к каждому клиенту.',
  aboutShortTitle: 'Профессиональный косметолог',
  withExperience: 'с опытом',
    viewServices: 'Посмотреть услуги',
  // Follow Us CTA
  followUs: 'Подпишитесь на меня',
  followUsDesc: 'Подписывайтесь в Instagram и Facebook, чтобы видеть последние результаты и предложения.',
    
    // Services Section
    servicesTitle: 'Услуги',
    facialCleaning: 'Глубокая чистка лица',
    facialCleaningDesc: 'Профессиональная чистка лица современными методами',
    antiAging: 'Антивозрастные процедуры',
    antiAgingDesc: 'Эффективные процедуры для сохранения молодости кожи',
    skinAnalysis: 'Анализ кожи',
    skinAnalysisDesc: 'Детальный анализ состояния кожи и рекомендации',
  servicesHeading: 'Наши услуги',
  servicesSubtitle: 'Индивидуальный подход к каждому клиенту с использованием современных методов',
  featureDeepCleaning: 'Глубокая чистка',
  featureFaceMassage: 'Массаж лица',
  featureHydration: 'Увлажнение',
  featureRF: 'RF-лифтинг',
  featureMesotherapy: 'Мезотерапия',
  featureLED: 'LED-терапия',
  featureSkinAnalysis: 'Анализ кожи',
  featureConsultation: 'Консультация',
  featureRecommendations: 'Рекомендации',
  reserveButton: 'Записаться',
  bookNow: 'Записаться',
  freeText: 'Бесплатно',
  chooseServiceCTA: 'Выберите услугу и забронируйте удобное время.',
    
    // Gallery Section
    galleryTitle: 'До/После',
    gallerySubtitle: 'Результаты говорят сами за себя',
      galleryDesc1: 'Глубокая чистка лица - результат через 3 месяца',
      galleryDesc2: 'RF-лифтинг - результат через 6 недель',
      galleryDesc3: 'Мезотерапия - результат через 2 месяца',
      galleryDesc4: 'LED-терапия - результат через 4 недели',
      before: 'До',
      after: 'После',
      galleryDisclaimer: '* Результаты могут отличаться в зависимости от индивидуальных особенностей',
      galleryCta: 'Готовы начать путь к красоте?',
      bookConsultation: 'Записаться на консультацию',
    
  // Testimonials Section (removed)
    
  // Booking Section
  bookingTitle: 'Записаться на приём',
  bookingSubtitle: 'Выберите удобное для вас время',
  noAvailableSlots: 'Нет доступных слотов',
  name: 'Имя',
  surname: 'Фамилия',
  email: 'Email',
  phone: 'Телефон',
  service: 'Услуга',
  date: 'Дата',
  time: 'Время',
  message: 'Сообщение',
  submit: 'Отправить заявку',
  visitType: 'Это первый визит?',
  visitTypeFirst: 'Первый визит',
  visitTypeRepeat: 'Повторный визит',
  acceptRules: 'Я ознакомился с правилами записи',
    
    // Footer
  contact: 'Контакты',
  address: 'Адрес: Рига, Латвия',
  phone_number: 'Телефон: +371 26094018',
  email_address: 'Email: <EMAIL>',
  selectOption: 'Выберите...',
  addressLabel: 'Адрес',
  addressValue: 'Bruņinieku iela 27, Рига',
  phoneLabel: 'Телефон',
  phoneValue: '+37126094018',
  emailLabel: 'Email',
  emailValue: '<EMAIL>',
  cityName: 'Рига',
  brandDesc: 'Профессиональная косметология в Риге. Моя цель — помочь вам раскрыть красоту и чувствовать себя уверенно в своей коже.',
  quickLinksTitle: 'Быстрые ссылки',
  copyrightText: '© 2024 About your skin. Все права защищены.',
  developedWith: 'Создано с помощью',
  close: 'Закрыть',
  thankYou: 'Спасибо! Мы свяжемся с вами в ближайшее время.',
  messagePlaceholder: 'Дополнительная информация или вопросы...',
  callUs: 'В случае вопросов:',
  visitTypeRequired: 'Пожалуйста, укажите, первый это визит или повторный',
  fillRequired: 'Пожалуйста, заполните все обязательные поля',
  bookingSuccess: 'Спасибо за вашу резервацию',
  acceptRulesRequired: 'Пожалуйста, отметьте, что вы ознакомились с правилами записи',
  dateFullyBooked: 'Все слоты на эту дату заняты.',
  },
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = useState<Language>('lv');

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['lv']] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
