import type { Metadata, Viewport } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";
import { LanguageProvider } from "@/contexts/LanguageContext";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const playfair = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair",
});

export const metadata: Metadata = {
  title: "About your skin - Profesionāla kosmetoloģija Rīgā",
  description: "Profesionāla sejas kopšana un косmetoloģijas pakalpojumi Rīgā. Sertificēta kosmetoloģe ar 10+ gadu pieredzi. Rezervējiet vizīti jau šodien!",
  keywords: "kosmetoloģija, sejas kopšana, R<PERSON>ga, RF lifting, mezoterapija, LED terapija, ādas analīze",
  authors: [{ name: "About your skin" }],
  robots: "index, follow",
  openGraph: {
    title: "About your skin - Profesionāla kosmetoloģija Rīgā",
    description: "Profesionāla sejas kopšana un kosmetoloģijas pakalpojumi Rīgā",
    type: "website",
    locale: "lv_LV",
    siteName: "About your skin",
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="lv">
      <body className={`${inter.variable} ${playfair.variable} antialiased`}>
        <LanguageProvider>
          {children}
        </LanguageProvider>
      </body>
    </html>
  );
}
