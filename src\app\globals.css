
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #1a1625;
  --foreground: #f8f7ff;
  /* Replace purple/lilac variables with light gray palette */
  --primary: #d9d9dd;
  --secondary: #cfcfd3;
  --accent: #e6e6e8;
  --muted: #2d253a;
  --border: #3d3446;
}

* {
  box-sizing: border-box;
}

body {
  background: #23272A; /* мокрый асфальт */
  color: var(--foreground);
  font-family: 'Inter', sans-serif;
  cursor: auto;
  overflow-x: hidden;
}


/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Selection styling */
::selection {
  background: rgba(220, 220, 225, 0.35); /* light gray selection */
  color: var(--foreground);
}
