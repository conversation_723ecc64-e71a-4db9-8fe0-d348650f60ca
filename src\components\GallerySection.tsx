'use client';

import { motion } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';
import { ArrowLeft, ArrowRight, Image as ImageIcon } from 'lucide-react';
import { useState } from 'react';

export default function GallerySection() {
  const { t } = useLanguage();
  const [currentIndex, setCurrentIndex] = useState(0);

  // Placeholder gallery items
    const galleryItems = [
    {
      id: 1,
      before: 'Before treatment 1',
      after: 'After treatment 1',
      description: t('galleryDesc1'),
    },
    {
      id: 2,
      before: 'Before treatment 2',
      after: 'After treatment 2',
      description: t('galleryDesc2'),
    },
    {
      id: 3,
      before: 'Before treatment 3',
      after: 'After treatment 3',
      description: t('galleryDesc3'),
    },
    {
      id: 4,
      before: 'Before treatment 4',
      after: 'After treatment 4',
      description: t('galleryDesc4'),
    },
  ];

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % galleryItems.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + galleryItems.length) % galleryItems.length);
  };

  return (
    <section id="gallery" className="py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#ffe4ec33' }}>
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            className="inline-block px-4 py-2 bg-accent/10 border border-accent/20 rounded-full text-accent text-sm font-medium mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            {t('gallery')}
          </motion.div>

          <motion.h2
            className="text-4xl md:text-5xl font-serif text-foreground mb-6 leading-tight"
            style={{ fontFamily: "'Playfair Display', serif" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            {t('galleryTitle')}
          </motion.h2>

          <motion.p
            className="text-lg text-foreground/80 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            {t('gallerySubtitle')}
          </motion.p>
        </div>

        {/* Gallery Carousel */}
        <motion.div
          className="relative"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          {/* Main Gallery Item */}
          <div className="relative bg-gradient-to-br from-background to-muted/30 p-8 rounded-3xl border border-border/50 backdrop-blur-sm">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              {/* Before Image */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-foreground/80 text-center">
                  {t('before')}
                </h3>
                <div className="aspect-[4/5] bg-gradient-to-br from-muted/30 to-muted/50 rounded-2xl border border-border/30 flex items-center justify-center">
                  <div className="text-center text-foreground/40">
                    <ImageIcon className="w-16 h-16 mx-auto mb-4" />
                    <p className="text-sm">[{galleryItems[currentIndex].before}]</p>
                    <p className="text-xs mt-1">Foto placeholder</p>
                  </div>
                </div>
              </div>

              {/* After Image */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-accent text-center">
                  {t('after')}
                </h3>
                <div className="aspect-[4/5] bg-gradient-to-br from-accent/20 to-accent/30 rounded-2xl border border-accent/30 flex items-center justify-center">
                  <div className="text-center text-accent/60">
                    <ImageIcon className="w-16 h-16 mx-auto mb-4" />
                    <p className="text-sm">[{galleryItems[currentIndex].after}]</p>
                    <p className="text-xs mt-1">Foto placeholder</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Description */}
            <div className="text-center mt-8">
              <h4 className="text-xl font-medium text-foreground mb-2">
                {galleryItems[currentIndex].description}
              </h4>
              <p className="text-foreground/70">
                {t('galleryDisclaimer')}
              </p>
            </div>
          </div>

          {/* Navigation Controls */}
          <div className="flex items-center justify-between mt-8">
            <motion.button
              onClick={prevSlide}
              className="p-3 bg-background/30 border border-border/50 rounded-full hover:border-accent/50 hover:bg-accent/10 transition-all duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ArrowLeft className="w-5 h-5 text-foreground" />
            </motion.button>
            {/* Pagination Dots */}
            <div className="flex space-x-2">
              {galleryItems.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentIndex
                      ? 'bg-accent'
                      : 'bg-foreground/20 hover:bg-foreground/40'
                  }`}
                />
              ))}
            </div>

            <motion.button
              onClick={nextSlide}
              className="p-3 bg-background/30 border border-border/50 rounded-full hover:border-accent/50 hover:bg-accent/10 transition-all duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ArrowRight className="w-5 h-5 text-foreground" />
            </motion.button>
          </div>

          {/* Gallery Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-12">
            {galleryItems.map((item, index) => (
              <motion.button
                key={item.id}
                onClick={() => setCurrentIndex(index)}
                className={`aspect-square bg-gradient-to-br from-muted/30 to-muted/50 rounded-xl border transition-all duration-300 flex items-center justify-center ${
                  index === currentIndex
                    ? 'border-accent bg-accent/10'
                    : 'border-border/30 hover:border-accent/50'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-center text-foreground/40">
                  <ImageIcon className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-xs">Foto {index + 1}</p>
                </div>
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
        >
          <p className="text-foreground/70 mb-6">
            {t('galleryCta')}
          </p>
          <motion.button
            className="bg-accent/70 text-background px-8 py-4 rounded-full font-medium hover:bg-accent/60 transition-all duration-300 shadow-lg hover:shadow-accent/5"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => document.querySelector('#booking')?.scrollIntoView({ behavior: 'smooth' })}
          >
            {t('bookConsultation')}
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
