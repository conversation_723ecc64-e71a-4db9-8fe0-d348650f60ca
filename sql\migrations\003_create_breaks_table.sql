-- 003_create_breaks_table.sql
-- Create breaks table for managing breaks/pauses during work days (e.g., lunch breaks)

CREATE TABLE IF NOT EXISTS breaks (
  id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  date date NOT NULL,
  start_time time NOT NULL,
  end_time time NOT NULL,
  reason text DEFAULT 'Перерыв',
  created_by text,
  created_at timestamptz DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_breaks_date ON breaks(date);
CREATE INDEX IF NOT EXISTS idx_breaks_date_time ON breaks(date, start_time, end_time);

-- Add constraint to ensure start_time < end_time
ALTER TABLE breaks ADD CONSTRAINT check_breaks_time_order CHECK (start_time < end_time);
