'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Menu, X, Globe } from 'lucide-react';

export default function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { language, setLanguage, t } = useLanguage();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { key: 'about', href: '#about' },
    { key: 'services', href: '#services' },
    { key: 'gallery', href: '#gallery' },
    { key: 'booking', href: '#booking' },
  ];

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      <motion.nav
        className={`fixed top-0 left-0 right-0 z-40 transition-all duration-300 ${
          isScrolled ? 'bg-background/80 backdrop-blur-md border-b border-border' : 'bg-transparent'
        }`}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <motion.div
              className="flex-shrink-0"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <button
                onClick={() => scrollToSection('#hero')}
                className="text-xl font-serif text-accent font-semibold tracking-wide"
                style={{ fontFamily: "'Playfair Display', serif" }}
              >
                About your skin
              </button>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                {navItems.map((item) => (
                  <motion.button
                    key={item.key}
                    onClick={() => scrollToSection(item.href)}
                    className="text-foreground/80 hover:text-accent transition-colors duration-300 text-sm font-medium tracking-wide"
                    whileHover={{ y: -2 }}
                    whileTap={{ y: 0 }}
                  >
                    {t(item.key)}
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Language Switcher & Mobile Menu */}
            <div className="flex items-center space-x-4">
              {/* Language Switcher */}
              <div className="flex items-center space-x-2 bg-muted/50 rounded-full px-3 py-1">
                <Globe className="w-4 h-4 text-accent" />
                <button
                  onClick={() => setLanguage('lv')}
                  className={`text-xs font-medium transition-colors ${
                    language === 'lv' ? 'text-accent' : 'text-foreground/60 hover:text-foreground'
                  }`}
                >
                  LV
                </button>
                <span className="text-foreground/30">|</span>
                <button
                  onClick={() => setLanguage('ru')}
                  className={`text-xs font-medium transition-colors ${
                    language === 'ru' ? 'text-accent' : 'text-foreground/60 hover:text-foreground'
                  }`}
                >
                  RU
                </button>
              </div>

              {/* Mobile menu button */}
              <motion.button
                className="md:hidden p-2 rounded-lg bg-muted/50 text-foreground"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                whileTap={{ scale: 0.95 }}
              >
                {isMobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </motion.button>
            </div>
          </div>
        </div>
      </motion.nav>

      {/* Mobile Menu */}
      <motion.div
        className={`md:hidden fixed inset-0 z-30 bg-background/95 backdrop-blur-md ${
          isMobileMenuOpen ? 'pointer-events-auto' : 'pointer-events-none'
        }`}
        initial={{ opacity: 0 }}
        animate={{ opacity: isMobileMenuOpen ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex flex-col items-center justify-center h-full space-y-8">
          {navItems.map((item, index) => (
            <motion.button
              key={item.key}
              onClick={() => scrollToSection(item.href)}
              className="text-2xl font-medium text-foreground hover:text-accent transition-colors"
              initial={{ opacity: 0, y: 20 }}
              animate={{
                opacity: isMobileMenuOpen ? 1 : 0,
                y: isMobileMenuOpen ? 0 : 20,
              }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              {t(item.key)}
            </motion.button>
          ))}
        </div>
      </motion.div>
    </>
  );
}
