import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { buildReminderSMS } from '@/lib/notifications';
import { sendSMSViaTwilio } from '@/lib/twilio';

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Проверяем авторизацию для CRON - поддерживаем и query parameter и header
    const authHeader = req.headers.authorization;
    const secretQuery = req.query.secret as string;
    const cronSecret = process.env.CRON_SECRET;
    
    if (!cronSecret) {
      console.error('❌ CRON_SECRET not configured');
      return res.status(500).json({ error: 'Cron secret not configured' });
    }

    // Проверяем секрет либо в заголовке, либо в query параметре
    const isAuthorized = 
      (authHeader === `Bearer ${cronSecret}`) || 
      (secretQuery === cronSecret);

    if (!isAuthorized) {
      console.error('❌ Unauthorized cron request');
      return res.status(401).json({ error: 'Unauthorized' });
    }

    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE) {
      console.error('Supabase env vars are missing');
      return res.status(500).json({ error: 'Supabase is not configured' });
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE);

    // Получаем завтрашнюю дату
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowDateStr = tomorrow.toISOString().split('T')[0]; // YYYY-MM-DD

    console.log('🔔 Checking for reminders for date:', tomorrowDateStr);

    // Найти все бронирования на завтра, которым ещё не отправили напоминание
    interface Booking {
      id: number;
      name: string;
      phone: string;
      date: string;
      time: string;
      language?: string | null;
      reminder_retry_count?: number | null;
      reminder_sent?: boolean | null;
    }

    const { data: bookings, error } = await supabase
      .from('bookings')
      .select('*')
      .eq('date', tomorrowDateStr)
      .eq('status', 'booked')
      .eq('reminder_sent', false);

    if (error) {
      console.error('❌ Error fetching bookings:', error);
      return res.status(500).json({ error: error.message });
    }

    if (!bookings || bookings.length === 0) {
      console.log('📭 No bookings found for reminders');
      return res.status(200).json({ 
        message: 'No reminders to send', 
        count: 0 
      });
    }

    console.log(`📬 Found ${bookings.length} bookings for reminders`);

    let successCount = 0;
    let errorCount = 0;

    // Отправляем напоминания каждому клиенту
    for (const booking of (bookings as Booking[])) {
      try {
        console.log(`📤 Sending reminder to ${booking.name} (${booking.phone})`);

        // Формируем текст напоминания
        const reminderText = buildReminderSMS({
          name: booking.name,
          date: booking.date,
          time: booking.time,
          address: process.env.BUSINESS_ADDRESS || undefined,
          language: (booking.language as 'ru' | 'lv' | undefined) || 'ru'
        });

        // Отправляем SMS
        const smsSuccess = await sendSMSViaTwilio({ to: booking.phone, text: reminderText });

        if (smsSuccess) {
          // Помечаем напоминание как отправленное
          await supabase
            .from('bookings')
            .update({ 
              reminder_sent: true,
              reminder_sent_at: new Date().toISOString()
            })
            .eq('id', booking.id);

          console.log(`✅ Reminder sent successfully to ${booking.name}`);
          successCount++;
        } else {
          console.log(`❌ Failed to send reminder to ${booking.name}`);
          errorCount++;
          
          // Увеличиваем счётчик попыток
          await supabase
            .from('bookings')
            .update({
              reminder_retry_count: ((booking as Booking).reminder_retry_count || 0) + 1,
              last_reminder_error: 'SMS sending failed'
            })
            .eq('id', booking.id);
        }

      } catch (smsError: unknown) {
        const msg = smsError instanceof Error ? smsError.message : String(smsError);
        console.error(`❌ Error sending reminder to ${booking.name}:`, msg);
        errorCount++;

        // Сохраняем ошибку в БД
        await supabase
          .from('bookings')
          .update({
            reminder_retry_count: (((booking as Booking).reminder_retry_count) || 0) + 1,
            last_reminder_error: msg || 'Unknown error'
          })
          .eq('id', booking.id);
      }
    }

    console.log(`📊 Reminder summary: ${successCount} sent, ${errorCount} failed`);

    return res.status(200).json({
      message: 'Reminders processed',
      total: bookings.length,
      sent: successCount,
      failed: errorCount,
      date: tomorrowDateStr
    });

  } catch (error: unknown) {
    const msg = error instanceof Error ? error.message : String(error);
    console.error('💥 Reminder API error:', msg);
    return res.status(500).json({ error: msg || 'Internal server error' });
  }
}
