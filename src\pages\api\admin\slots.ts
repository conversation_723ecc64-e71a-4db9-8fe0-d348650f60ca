import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_ROLE = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE) {
      console.error('Supabase env vars are missing: SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY')
      return res.status(500).json({ error: 'Supabase is not configured on the server' })
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE)

    if (req.method === 'GET') {
      const { data, error } = await supabase
        .from('slots')
        .select('*')
        .order('date', { ascending: true })
        .order('start_time', { ascending: true })

      if (error) {
        console.error('Supabase select slots error:', error)
        return res.status(500).json({ error: 'Failed to fetch slots' })
      }

      return res.status(200).json(data || [])
    }

    if (req.method === 'POST') {
      const { date, start_time, end_time, capacity = 1, created_by = 'admin' } = req.body
      
      if (!date || !start_time || !end_time) {
        return res.status(400).json({ error: 'Missing required fields: date, start_time, end_time' })
      }

      // Проверяем, что время начала меньше времени окончания
      if (start_time >= end_time) {
        return res.status(400).json({ error: 'Start time must be before end time' })
      }

      // Проверяем, нет ли пересекающихся слотов на ту же дату
      const { data: existingSlots, error: checkError } = await supabase
        .from('slots')
        .select('*')
        .eq('date', date)
        .or(`and(start_time.lte.${end_time},end_time.gt.${start_time})`)

      if (checkError) {
        console.error('Supabase check slots error:', checkError)
        return res.status(500).json({ error: 'Failed to check existing slots' })
      }

      if (existingSlots && existingSlots.length > 0) {
        return res.status(409).json({ error: 'Time slot conflicts with existing slot' })
      }

      const { data, error } = await supabase
        .from('slots')
        .insert([{ date, start_time, end_time, capacity, created_by }])
        .select()
        .single()

      if (error) {
        console.error('Supabase insert slot error:', error)
        return res.status(500).json({ error: 'Failed to create slot' })
      }

      return res.status(201).json(data)
    }

    if (req.method === 'DELETE') {
      const { id, date, breakId } = req.query

      // Удаление слота по ID
      if (id) {
        if (isNaN(Number(id))) {
          return res.status(400).json({ error: 'Invalid ID' })
        }

        // Проверяем, есть ли активные бронирования для этого слота
        const { count, error: countError } = await supabase
          .from('bookings')
          .select('id', { count: 'exact', head: true })
          .eq('slot_id', Number(id))
          .eq('status', 'booked')

        if (countError) {
          console.error('Supabase count bookings error:', countError)
          return res.status(500).json({ error: 'Failed to check slot bookings' })
        }

        if (count && count > 0) {
          return res.status(409).json({ error: 'Cannot delete slot with active bookings' })
        }

        const { error } = await supabase
          .from('slots')
          .delete()
          .eq('id', Number(id))

        if (error) {
          console.error('Supabase delete slot error:', error)
          return res.status(500).json({ error: 'Failed to delete slot' })
        }

        return res.status(200).json({ success: true })
      }

      // Удаление всех слотов по дате
      if (date) {
        // Проверяем, есть ли активные бронирования для слотов этой даты
        const { data: slotsToDelete, error: fetchError } = await supabase
          .from('slots')
          .select('id')
          .eq('date', date)

        if (fetchError) {
          console.error('Supabase fetch slots error:', fetchError)
          return res.status(500).json({ error: 'Failed to fetch slots for date' })
        }

        if (slotsToDelete && slotsToDelete.length > 0) {
          const slotIds = slotsToDelete.map(slot => slot.id)
          
          const { count, error: countError } = await supabase
            .from('bookings')
            .select('id', { count: 'exact', head: true })
            .in('slot_id', slotIds)
            .eq('status', 'booked')

          if (countError) {
            console.error('Supabase count bookings error:', countError)
            return res.status(500).json({ error: 'Failed to check slot bookings' })
          }

          if (count && count > 0) {
            return res.status(409).json({ error: 'Cannot delete slots with active bookings' })
          }
        }

        const { error } = await supabase
          .from('slots')
          .delete()
          .eq('date', date)

        if (error) {
          console.error('Supabase delete slots by date error:', error)
          return res.status(500).json({ error: 'Failed to delete slots' })
        }

        return res.status(200).json({ success: true })
      }

      // Удаление break по ID
      if (breakId) {
        if (isNaN(Number(breakId))) {
          return res.status(400).json({ error: 'Invalid break ID' })
        }

        const { error } = await supabase
          .from('breaks')
          .delete()
          .eq('id', Number(breakId))

        if (error) {
          console.error('Supabase delete break error:', error)
          return res.status(500).json({ error: 'Failed to delete break' })
        }

        return res.status(200).json({ success: true })
      }

      return res.status(400).json({ error: 'Missing required parameter: id, date, or breakId' })
    }

    return res.status(405).json({ error: 'Method not allowed' })
  } catch (error) {
    console.error('Unexpected error in slots API:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
