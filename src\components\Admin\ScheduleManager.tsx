"use client"
import React, { useEffect, useState } from 'react'
import Calendar from './Calendar'

type Slot = { id?: number; date: string; start_time: string; end_time: string; capacity: number }
type Block = { id?: number; date: string; reason?: string }
type Break = { id?: number; date: string; start_time: string; end_time: string; reason?: string }

export default function ScheduleManager() {
  const [slots, setSlots] = useState<Slot[]>([])
  const [blocks, setBlocks] = useState<Block[]>([])
  const [breaks, setBreaks] = useState<Break[]>([])
   const [selected, setSelected] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [refreshTimeout, setRefreshTimeout] = useState<NodeJS.Timeout | null>(null)

  useEffect(() => { 
  fetchList()
    
    // Cleanup timeout при размонтировании компонента
    return () => {
      if (refreshTimeout) {
        clearTimeout(refreshTimeout)
      }
    }
  }, [refreshTimeout])

  // Функция для отложенного обновления (debounce)
  function scheduleRefresh() {
    if (refreshTimeout) {
      clearTimeout(refreshTimeout)
    }
    const timeout = setTimeout(() => {
      // Silent refresh to avoid unmounting child UI
      fetchList(true)
    }, 100) // Обновляем через 100мс после последнего вызова (снижаем задержку для более быстрого отклика)
    setRefreshTimeout(timeout)
  }
  async function fetchList(silent = false) {
    if (!silent) setLoading(true)
    setError(null)
    try {
      // Делаем все запросы параллельно для ускорения
      const [slotsRes, blocksRes, breaksRes] = await Promise.all([
        fetch('/api/admin/slots').catch(() => null),
        fetch('/api/admin/blocks').catch(() => null), 
        fetch('/api/admin/breaks').catch(() => null)
      ])

      // Обрабатываем результаты
      let slotsResponse = []
      let blocksResponse = []
      let breaksResponse = []

      if (slotsRes?.ok) {
        slotsResponse = await slotsRes.json()
      }
      
      if (blocksRes?.ok) {
        blocksResponse = await blocksRes.json()
      }
      
      if (breaksRes?.ok) {
        breaksResponse = await breaksRes.json()
      }
      
      // Убеждаемся, что ответы являются массивами
      setSlots(Array.isArray(slotsResponse) ? slotsResponse : [])
      setBlocks(Array.isArray(blocksResponse) ? blocksResponse : [])
      setBreaks(Array.isArray(breaksResponse) ? breaksResponse : [])
    } catch (error: unknown) { 
      console.error('Failed to fetch data:', error)
      setError(error instanceof Error ? error.message : 'Не удалось загрузить данные. Попробуйте обновить страницу.')
      setSlots([])
      setBlocks([])
      setBreaks([])
    } finally {
      if (!silent) setLoading(false)
    }
  }

  async function createSlot(slot: Slot) {
    try {
      const res = await fetch('/api/admin/slots', { 
        method: 'POST', 
        headers: { 'Content-Type': 'application/json' }, 
        body: JSON.stringify(slot) 
      })
      
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Не удалось создать слот')
      }
  const created = await res.json()
  scheduleRefresh() // Используем debounced обновление
  return created
    } catch (error: unknown) {
      console.error('Failed to create slot:', error)
      setError(error instanceof Error ? error.message : 'Ошибка при создании слота')
      throw error // Re-throw to allow Calendar component to handle it
    }
  }

  async function createBlock(b: Block) {
    // Optimistic create: add temporary block to UI immediately, then replace with server result
    const tempId = -(Date.now())
    const tempBlock: Block = { id: tempId, ...b }
    const prev = blocks
    try {
      setBlocks(prevBlocks => [...prevBlocks, tempBlock])
      const res = await fetch('/api/admin/blocks', { 
        method: 'POST', 
        headers: { 'Content-Type': 'application/json' }, 
        body: JSON.stringify(b) 
      })

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}))
        throw new Error(errorData.error || 'Не удалось создать блокировку')
      }

      const created = await res.json()
      // Replace temp block with created block from server
      setBlocks(prevBlocks => prevBlocks.map(bl => bl.id === tempId ? created : bl))
      scheduleRefresh() // keep canonical data fresh
      return created
    } catch (error: unknown) {
      console.error('Failed to create block:', error)
      setError(error instanceof Error ? error.message : 'Ошибка при создании блокировки')
      // revert optimistic add
      setBlocks(prev)
      throw error // Re-throw to allow Calendar component to handle it
    }
  }

  // Optimistic delete for a block (so calendar grid updates immediately)
  async function deleteBlock(id: number) {
    const prev = blocks
    try {
      // optimistic removal
      setBlocks(prevBlocks => prevBlocks.filter(b => b.id !== id))
      const res = await fetch(`/api/admin/blocks?id=${id}`, { method: 'DELETE' })
      if (!res.ok) {
        const text = await res.text().catch(() => '')
        throw new Error(text || 'Не удалось удалить блок')
      }
  // Fetch updated canonical data immediately so calendar reflects change
  await fetchList()
    } catch (error: unknown) {
      console.error('Failed to delete block:', error)
      setError(error instanceof Error ? error.message : 'Ошибка при удалении блокировки')
      // revert optimistic change
      setBlocks(prev)
      throw error
    }
  }

  async function createBreak(breakData: Break) {
    try {
      console.log('🚀 Creating break:', breakData)
      const res = await fetch('/api/admin/breaks', { 
        method: 'POST', 
        headers: { 'Content-Type': 'application/json' }, 
        body: JSON.stringify(breakData) 
      })
      
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Не удалось создать перерыв')
      }
      
      const result = await res.json()
      console.log('✅ Break created successfully:', result)
      
      console.log('🔄 Scheduling data refresh...')
      scheduleRefresh() // Используем debounced обновление
      console.log('📊 Breaks will update after:', breaks)
    } catch (error: unknown) {
      console.error('Failed to create break:', error)
      setError(error instanceof Error ? error.message : 'Ошибка при создании перерыва')
      throw error // Re-throw to allow Calendar component to handle it
    }
  }

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900/20 p-6 rounded-2xl shadow-2xl border border-gray-700/50">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="bg-gray-800/50 backdrop-blur-sm p-6 rounded-xl border border-gray-600/30">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gray-700/50 rounded-lg animate-pulse"></div>
                  <div className="w-32 h-6 bg-gray-700/50 rounded animate-pulse"></div>
                  <div className="w-10 h-10 bg-gray-700/50 rounded-lg animate-pulse"></div>
                </div>
              </div>
              <div className="grid grid-cols-7 gap-2">
                {Array.from({ length: 42 }).map((_, i) => (
                  <div key={i} className="w-full h-12 bg-gray-700/30 rounded-lg animate-pulse"></div>
                ))}
              </div>
            </div>
          </div>
          <div className="bg-gray-800/50 backdrop-blur-sm p-6 rounded-xl border border-gray-600/30">
            <div className="space-y-4">
              <div className="w-full h-6 bg-gray-700/50 rounded animate-pulse"></div>
              <div className="w-3/4 h-6 bg-gray-700/50 rounded animate-pulse"></div>
              <div className="w-1/2 h-6 bg-gray-700/50 rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div>
      {error && (
        <div className="mb-4 p-4 bg-red-900/50 border border-red-700/50 rounded-lg text-red-200">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <button 
              onClick={() => setError(null)}
              className="text-red-400 hover:text-red-300"
            >
              ✕
            </button>
          </div>
        </div>
      )}
      
      <Calendar
        slots={slots}
        blocks={blocks}
        breaks={breaks}
    selected={selected}
    onSelect={setSelected}
  onRefresh={() => scheduleRefresh()}
        onCreateSlot={async (slot: { date: string; start_time: string; end_time: string; capacity?: number }) => { 
          await createSlot(slot as Slot) 
        }}
        onCreateBlock={async (b: { date: string; reason?: string }) => { 
          await createBlock(b as Block) 
        }}
        onDeleteBlockParent={async (id: number) => { await deleteBlock(id) }}
        onCreateBreak={async (breakData: { date: string; start_time: string; end_time: string; reason?: string }) => { 
          await createBreak(breakData as Break) 
        }}
      />
    </div>
  )
}
