-- 002_create_blocks_table.sql
-- Create blocks table for managing blocked dates

CREATE TABLE IF NOT EXISTS blocks (
  id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  date date NOT NULL,
  reason text,
  created_by text,
  created_at timestamptz DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_blocks_date ON blocks(date);

-- Add unique constraint to prevent duplicate blocks on the same date
CREATE UNIQUE INDEX IF NOT EXISTS unique_blocks_date ON blocks(date);
