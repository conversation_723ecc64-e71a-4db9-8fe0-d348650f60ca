'use client';

import { motion } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';
import { 
  MapPin, 
  Phone, 
  Mail, 
  Instagram, 
  Facebook, 
  Heart 
} from 'lucide-react';
import Image from 'next/image';

// Render the provided WhatsApp logo from /public as an inline component so
// it can be used where other icons are used (accepts className prop).
function WhatsAppImage({ className }: { className?: string }) {
  // encodeURI because filename contains spaces and special characters
  const src = encodeURI('/WhatsApp-Logo in Schwarz-Weiß.png');
  return (
    <Image
      src={src}
      alt="WhatsApp"
      width={32}
      height={32}
      className={className}
      style={{ objectFit: 'contain' }}
    />
  );
}

export default function Footer() {
  const { t } = useLanguage();

  const contactInfo = [
      {
        icon: MapPin,
        label: t('addressLabel'),
        value: t('addressValue'),
        href: '#',
        rowClass: 'flex items-start gap-2 text-foreground/70 hover:text-accent transition-colors duration-300 group',
        iconClass: 'w-5 h-5 object-contain group-hover:scale-110 transition-transform',
      },
    {
      icon: Phone,
      label: t('phoneLabel'),
      value: t('phoneValue'),
      href: `tel:${t('phoneValue').replace(/\s|\+/g, '')}`,
      rowClass: 'flex items-start gap-2 text-foreground/70 hover:text-accent transition-colors duration-300 group',
      iconClass: 'w-5 h-5 object-contain group-hover:scale-110 transition-transform',
    },
    {
      icon: WhatsAppImage,
      label: 'WhatsApp',
      // larger icon specifically for WhatsApp
      iconClass: 'w-8 h-8 object-contain group-hover:scale-110 transition-transform',
      // normalized row class for consistent left alignment
      rowClass: 'flex items-start gap-2 text-foreground/70 hover:text-accent transition-colors duration-300 group',
      value: `+371 ${t('phoneValue').replace(/\+?371?/, '').replace(/\s/g, '')}`,
      href: `https://wa.me/${t('phoneValue').replace(/\D/g, '')}`,
    },
    {
      icon: Mail,
      label: t('emailLabel'),
      value: t('emailValue'),
      href: `mailto:${t('emailValue')}`,
      rowClass: 'flex items-start gap-2 text-foreground/70 hover:text-accent transition-colors duration-300 group',
      iconClass: 'w-5 h-5 object-contain group-hover:scale-110 transition-transform',
    },
  ];

  // workingHours removed

  const quickLinks = [
    { name: t('about'), href: '#about' },
    { name: t('services'), href: '#services' },
    { name: t('gallery'), href: '#gallery' },
    { name: t('booking'), href: '#booking' },
  ];

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-gradient-to-br from-background to-muted/30 border-t border-border/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-12">
          {/* Brand Section */}
          <motion.div
            className="lg:col-span-1"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h3 
              className="text-2xl font-serif text-accent mb-4"
              style={{ fontFamily: "'Playfair Display', serif" }}
            >
              About your skin
            </h3>
            <p className="text-foreground/70 mb-6 leading-relaxed">
              {t('brandDesc')}
            </p>
            
            {/* Social Links */}
            <div className="flex space-x-4">
              <motion.a
                href="https://www.instagram.com/__aboutyourskin?igsh=MXR3bWVmbmlkNWExOA=="
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-accent/10 border border-accent/20 rounded-full flex items-center justify-center text-accent hover:bg-accent hover:text-background transition-all duration-300"
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.9 }}
              >
                <Instagram className="w-5 h-5" />
              </motion.a>
              <motion.a
                href="https://www.facebook.com/share/16C44uRfQE/?mibextid=wwXIfr"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-accent/10 border border-accent/20 rounded-full flex items-center justify-center text-accent hover:bg-accent hover:text-background transition-all duration-300"
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.9 }}
              >
                <Facebook className="w-5 h-5" />
              </motion.a>
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <h4 className="text-lg font-semibold text-foreground mb-6">
              {t('quickLinksTitle')}
            </h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <motion.button
                    onClick={() => scrollToSection(link.href)}
                    className="text-foreground/70 hover:text-accent transition-colors duration-300 text-left"
                    whileHover={{ x: 5 }}
                  >
                    {link.name}
                  </motion.button>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h4 className="text-lg font-semibold text-foreground mb-6">
              {t('contact')}
            </h4>
            <ul className="space-y-4">
              {contactInfo.map((item, index) => {
                const Icon = item.icon;
                return (
                  <li key={index}>
                    <motion.a
                      href={item.href}
                      className={item.rowClass ?? 'flex items-start space-x-3 text-foreground/70 hover:text-accent transition-colors duration-300 group'}
                      whileHover={{ x: 5 }}
                    >
                      {/* icon column: fixed width so icons align vertically */}
                      <div className="w-8 flex items-center justify-center">
                        <Icon className={item.iconClass ?? 'w-5 h-5 text-accent group-hover:scale-110 transition-transform'} />
                      </div>
                      <div>
                        <div className="text-sm font-medium">{item.label}</div>
                        <div className="text-sm">{item.value}</div>
                      </div>
                    </motion.a>
                  </li>
                );
              })}
            </ul>
          </motion.div>

          {/* Working hours and emergency contact removed */}
        </div>

        {/* Bottom Section */}
        <motion.div
          className="border-t border-border/30 mt-12 pt-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="text-foreground/60 text-sm mb-4 md:mb-0">
              {t('copyrightText')}
            </div>
            
            <div className="flex items-center space-x-1 text-foreground/60 text-sm">
              <span>{t('developedWith')}</span>
              <Heart className="w-4 h-4 text-accent fill-current" />
              <span>{t('cityName')}</span>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Back to Top Button */}
      <motion.button
  className="fixed bottom-8 right-8 w-12 h-12 bg-accent/40 text-background rounded-full shadow-lg hover:shadow-accent/5 flex items-center justify-center transition-all duration-300 z-40"
        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        initial={{ opacity: 0, scale: 0 }}
        whileInView={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6, delay: 0.5 }}
        viewport={{ once: true }}
        whileHover={{ scale: 1.1, y: -2 }}
        whileTap={{ scale: 0.9 }}
      >
        <svg 
          className="w-6 h-6" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M5 10l7-7m0 0l7 7m-7-7v18" 
          />
        </svg>
      </motion.button>
    </footer>
  );
}
