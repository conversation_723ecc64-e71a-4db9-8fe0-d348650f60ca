'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'

// Утилита для форматирования времени в короткий формат HH:MM
function formatShortTime(timeStr?: string): string {
  if (!timeStr) return ''
  const trimmed = timeStr.trim()
  if (/^\d{1,2}:\d{2}$/.test(trimmed)) {
    return trimmed.padStart(5, '0')
  }
  const match = trimmed.match(/^(\d{1,2}:\d{2})/)
  if (match) {
    return match[1].padStart(5, '0')
  }
  return trimmed
}

type NewBookingData = {
  name: string
  surname: string
  email: string
  phone: string
  countryCode: string
  visit_type: 'first' | 'repeat'
  date: string
  time: string
  message: string
  language: 'ru' | 'lv'
}

export default function NewBookingPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [showDateModal, setShowDateModal] = useState(false)
  const [availableSlots, setAvailableSlots] = useState<string[]>([])
  const [slotsLoading, setSlotsLoading] = useState(false)
  
  const [formData, setFormData] = useState<NewBookingData>({
    name: '',
    surname: '',
    email: '',
    phone: '',
    countryCode: '+371',
    visit_type: 'first',
    date: '',
    time: '',
    message: '',
    language: 'ru'
  })

  const fetchAvailableSlots = async (date: string) => {
    if (!date) {
      setAvailableSlots([])
      return
    }

    setSlotsLoading(true)
    try {
      const res = await fetch(`/api/bookings?date=${date}`)
      if (res.ok) {
        const data = await res.json()
        setAvailableSlots(data.availableTimes || [])
      } else {
        setAvailableSlots([])
      }
    } catch (e) {
      console.error('Error fetching slots:', e)
      setAvailableSlots([])
    } finally {
      setSlotsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name || !formData.phone || !formData.date || !formData.time) {
      setError('Заполните обязательные поля: имя, телефон, дату и время')
      return
    }

    // Проверяем номер телефона
    if (!formData.phone || formData.phone.trim().length < 4) {
      setError('Введите корректный номер телефона (минимум 4 цифры)')
      return
    }

    setLoading(true)
    setError('')
    
    try {
      // Формируем номер телефона правильно
      let finalPhone = '';
      
      // Если пользователь ввёл полный номер с кодом страны
      if (formData.phone.startsWith('+')) {
        finalPhone = formData.phone; // Используем как есть
      } else {
        // Если ввёл только номер без кода страны, добавляем выбранный код
        finalPhone = formData.countryCode + formData.phone.replace(/^\+/, '');
      }
      
      console.log('📱 Admin phone formatting:', {
        original: formData.phone,
        countryCode: formData.countryCode,
        final: finalPhone,
        startsWithPlus: formData.phone.startsWith('+')
      });

      const res = await fetch('/api/bookings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: formData.name,
          surname: formData.surname || null,
          email: formData.email || null,
          phone: finalPhone, // Используем правильно сформированный номер
          visitType: formData.visit_type,
          date: formData.date,
          time: formData.time,
          message: formData.message || null,
          language: formData.language
        })
      })
      
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Ошибка создания бронирования')
      }
      
      setSuccess(true)
      setTimeout(() => {
        router.push('/admin')
      }, 2000)
      
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Ошибка создания бронирования')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900/10 text-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-light text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 to-emerald-600 mb-2">
            Бронирование создано успешно!
          </h2>
          <p className="text-gray-400">Перенаправление на главную страницу...</p>
        </div>
      </main>
    )
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900/10 text-gray-100">
      <div className="max-w-4xl mx-auto p-6">
        {/* Заголовок */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <button
              onClick={() => router.push('/admin')}
              className="p-2 hover:bg-gray-700/50 rounded-lg transition-colors"
            >
              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
            </button>
            <h1 className="text-3xl font-light text-transparent bg-clip-text bg-gradient-to-r from-pink-400 via-purple-400 to-indigo-400">
              Создать новое бронирование
            </h1>
          </div>
          <p className="text-gray-400">Добавьте новую запись в систему</p>
        </div>

        {/* Форма */}
        <div className="bg-gradient-to-br from-gray-900/50 via-gray-800/50 to-purple-900/20 border border-gray-700/50 rounded-2xl p-8 backdrop-blur-sm shadow-2xl">
          {error && (
            <div className="mb-6 p-4 bg-gradient-to-r from-red-900/20 to-red-800/20 border border-red-500/30 rounded-xl text-red-400 text-sm backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <span className="text-red-500">❌</span>
                <span>{error}</span>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {/* Имя */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Имя <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-4 py-3 text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors"
                  placeholder="Введите имя"
                  required
                />
              </div>

              {/* Фамилия */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">Фамилия</label>
                <input
                  type="text"
                  value={formData.surname}
                  onChange={(e) => setFormData(prev => ({ ...prev, surname: e.target.value }))}
                  className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-4 py-3 text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors"
                  placeholder="Введите фамилию"
                />
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">Email</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-4 py-3 text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors"
                  placeholder="<EMAIL>"
                />
              </div>

              {/* Телефон */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Телефон <span className="text-red-400">*</span>
                </label>
                <div className="flex flex-row flex-nowrap gap-2 items-center">
                  <input
                    type="text"
                    value={formData.countryCode}
                    onChange={(e) => setFormData(prev => ({ ...prev, countryCode: e.target.value }))}
                    className="bg-gray-900/50 border border-gray-600/50 rounded-lg px-3 py-3 text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors w-28 flex-shrink-0 text-sm"
                    aria-label="Код страны"
                  />

                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    className="flex-1 min-w-0 bg-gray-900/50 border border-gray-600/50 rounded-lg px-4 py-3 text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors text-sm"
                    placeholder="12345678"
                    required
                  />
                </div>
              </div>

              {/* Дата */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Дата <span className="text-red-400">*</span>
                </label>
                <button
                  type="button"
                  onClick={() => setShowDateModal(true)}
                  className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-4 py-3 text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors text-left flex items-center justify-between"
                >
                  <span>{formData.date || 'Выберите дату'}</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </button>
              </div>

              {/* Время */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Время <span className="text-red-400">*</span>
                  {slotsLoading && <span className="ml-2 text-xs text-gray-500">(загрузка...)</span>}
                </label>
                {formData.date && availableSlots.length > 0 ? (
                  <select
                    value={formData.time}
                    onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                    className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-4 py-3 text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors"
                    required
                  >
                    <option value="">Выберите время</option>
                    {availableSlots.map(time => (
                      <option key={time} value={time}>
                        {formatShortTime(time)}
                      </option>
                    ))}
                  </select>
                ) : formData.date && !slotsLoading && availableSlots.length === 0 ? (
                  <div className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-4 py-3 text-gray-500">
                    Нет доступных слотов на выбранную дату
                  </div>
                ) : (
                  <input
                    type="time"
                    value={formData.time}
                    onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                    className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-4 py-3 text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors"
                    disabled={!formData.date}
                    required
                  />
                )}
              </div>

              {/* Тип визита */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">Тип визита</label>
                <select
                  value={formData.visit_type}
                  onChange={(e) => setFormData(prev => ({ ...prev, visit_type: e.target.value as 'first' | 'repeat' }))}
                  className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-4 py-3 text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors"
                >
                  <option value="first">🆕 Первый визит</option>
                  <option value="repeat">🔄 Повторный визит</option>
                </select>
              </div>

              {/* Язык уведомлений */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">Язык уведомлений</label>
                <select
                  value={formData.language}
                  onChange={(e) => setFormData(prev => ({ ...prev, language: e.target.value as 'ru' | 'lv' }))}
                  className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-4 py-3 text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors"
                >
                  <option value="ru">🇷🇺 Русский</option>
                  <option value="lv">🇱🇻 Latviešu</option>
                </select>
              </div>

              {/* Сообщение */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-3">Сообщение</label>
                <textarea
                  value={formData.message}
                  onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                  className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-4 py-3 text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors"
                  rows={4}
                  placeholder="Дополнительная информация..."
                />
              </div>
            </div>

            {/* Кнопки */}
            <div className="flex gap-4 justify-end">
              <button
                type="button"
                onClick={() => router.push('/admin')}
                className="px-6 py-3 border border-gray-600/50 text-gray-300 rounded-lg hover:bg-gray-700/30 transition-all duration-200"
              >
                Отмена
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-3 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg shadow-emerald-500/25"
              >
                {loading ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                    Создание...
                  </div>
                ) : (
                  'Создать бронирование'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Календарь */}
      <AnimatePresence>
        {showDateModal && (
          <motion.div 
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm" 
            initial={{ opacity: 0 }} 
            animate={{ opacity: 1 }} 
            exit={{ opacity: 0 }}
          >
            <motion.div 
              className="bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900/20 rounded-2xl shadow-2xl border border-gray-700/50 p-6 w-full max-w-md mx-4 backdrop-blur-sm" 
              initial={{ scale: 0.9, opacity: 0, y: 20 }} 
              animate={{ scale: 1, opacity: 1, y: 0 }} 
              exit={{ scale: 0.9, opacity: 0, y: 20 }} 
              transition={{ duration: 0.2, ease: "easeOut" }}
            >
              <CalendarModal
                value={formData.date}
                onSelect={(iso) => {
                  setFormData(prev => ({ ...prev, date: iso, time: '' }))
                  fetchAvailableSlots(iso)
                  setShowDateModal(false)
                }}
                onClose={() => setShowDateModal(false)}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </main>
  )
}

// Календарь для выбора даты
function CalendarModal({ value, onSelect, onClose }: { 
  value?: string
  onSelect: (iso: string) => void
  onClose: () => void 
}) {
  const today = new Date()
  const start = value ? new Date(value) : today
  const [monthDate, setMonthDate] = useState(new Date(start.getFullYear(), start.getMonth(), 1))
  const [fullyBookedDates, setFullyBookedDates] = useState<Record<string, boolean>>({})
  const [blockedDates, setBlockedDates] = useState<Record<string, boolean>>({})
  const [availableDates, setAvailableDates] = useState<Record<string, boolean>>({})

  // startDay: 0(Sun)..6(Sat) -> convert to Monday-first index 0(Mon)..6(Sun)
  const startDayRaw = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1).getDay()
  const startDay = (startDayRaw + 6) % 7
  const daysInMonth = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0).getDate()

  const prevMonth = () => setMonthDate(new Date(monthDate.getFullYear(), monthDate.getMonth() - 1, 1))
  const nextMonth = () => setMonthDate(new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 1))

  const isPast = (d: Date) => {
    const dd = new Date(d.getFullYear(), d.getMonth(), d.getDate())
    const t = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    return dd < t
  }

  const onPick = (day: number) => {
    const d = new Date(monthDate.getFullYear(), monthDate.getMonth(), day)
    if (isPast(d)) return
    // Format as local YYYY-MM-DD to avoid timezone shifts from toISOString()
    const iso = `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    if (!availableDates[iso] || fullyBookedDates[iso]) return
    onSelect(iso)
  }

  const monthLabel = monthDate.toLocaleString('ru-RU', { month: 'long', year: 'numeric' })

  const weekdayShort = (i: number) => {
    // i is 0..6 where 0 -> Monday
    const ru = ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс']
    return ru[i] || ''
  }

  const cells: Array<null | number> = []
  for (let i = 0; i < startDay; i++) cells.push(null)
  for (let d = 1; d <= daysInMonth; d++) cells.push(d)

  // Fetch admin slots, blocks and booking counts for the month to compute available dates
  useEffect(() => {
    const fetchMonth = async () => {
      const y = monthDate.getFullYear()
      const m = String(monthDate.getMonth() + 1).padStart(2, '0')
      const month = `${y}-${m}`
      try {
        const [bookingsRes, slotsRes, blocksRes] = await Promise.all([
          fetch(`/api/bookings?month=${month}`).catch(() => null),
          fetch('/api/admin/slots').catch(() => null),
          fetch('/api/admin/blocks').catch(() => null),
        ])

        const bookingsData = bookingsRes && bookingsRes.ok ? await bookingsRes.json().catch(() => ({})) : {}
        const rawCounts: Record<string, number> = bookingsData.counts || {}

        const allSlots = slotsRes && slotsRes.ok ? await slotsRes.json().catch(() => []) : []
        const allBlocks = blocksRes && blocksRes.ok ? await blocksRes.json().catch(() => []) : []

        // helper to normalize possible ISO timestamps to YYYY-MM-DD
        const norm = (val: unknown) => {
          if (val === undefined || val === null) return ''
          const s = String(val)
          if (s.includes('T')) return s.split('T')[0]
          return s
        }

        const slotsByDate: Record<string, number> = {}
        for (const slot of allSlots) {
          const key = norm(slot.date)
          if (key) slotsByDate[key] = (slotsByDate[key] || 0) + (slot.capacity || 1)
        }

        const blocked: Record<string, boolean> = {}
        for (const block of allBlocks) {
          const key = norm(block.date)
          if (key) blocked[key] = true
        }

        const counts: Record<string, number> = {}
        for (const [key, count] of Object.entries(rawCounts)) {
          const normKey = norm(key)
          if (normKey) counts[normKey] = count
        }

        // Calculate available and fully booked dates
        const fully: Record<string, boolean> = {}
        const avail: Record<string, boolean> = {}
        
        // Get all unique dates from slots, blocks, and bookings
        const allDates = new Set([
          ...Object.keys(slotsByDate),
          ...Object.keys(blocked),
          ...Object.keys(counts)
        ])

        for (const key of allDates) {
          const slotCount = slotsByDate[key] || 0
          const booked = counts[key] || 0
          const isBlocked = blocked[key] || false
          const isFully = isBlocked || slotCount === 0 || (slotCount > 0 && booked >= slotCount)
          fully[key] = isFully
          avail[key] = slotCount > 0 && !isBlocked && !isFully
        }

  setFullyBookedDates(fully)
  setBlockedDates(blocked)
  setAvailableDates(avail)
      } catch {
        // Ignore month fetch errors; UI will show empty availability
        setFullyBookedDates({})
        setBlockedDates({})
        setAvailableDates({})
      }
    }
    fetchMonth()
  }, [monthDate])

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-semibold text-gray-100">Выберите дату</h3>
        <button onClick={onClose} className="text-gray-400 hover:text-gray-200 transition-colors">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div className="flex items-center justify-between mb-4">
        <button type="button" onClick={prevMonth} className="text-purple-400 hover:text-purple-300 px-2 py-1 rounded">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <div className="text-gray-100 font-medium capitalize">{monthLabel}</div>
        <button type="button" onClick={nextMonth} className="text-purple-400 hover:text-purple-300 px-2 py-1 rounded">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      
      <div className="grid grid-cols-7 gap-1 text-center mb-2 text-sm text-gray-400">
        {[0,1,2,3,4,5,6].map((i) => (
          <div key={i} className="font-medium py-1">{weekdayShort(i)}</div>
        ))}
      </div>
      
      <div className="grid grid-cols-7 gap-1">
        {cells.map((c, i) => (
          <div key={i}>
            {c === null ? (
              <div className="h-10" />
            ) : (
              <button
                type="button"
                onClick={() => onPick(c)}
                title={fullyBookedDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`] ? 'Дата полностью занята' : undefined}
                className={`w-full h-10 rounded-lg text-sm relative flex items-center justify-center transition-all ${
                  isPast(new Date(monthDate.getFullYear(), monthDate.getMonth(), c)) 
                    ? 'text-gray-600 bg-gray-800/30 cursor-not-allowed' 
                    : fullyBookedDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`] 
                      ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed' 
                      : availableDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`]
                        ? 'bg-gray-800/50 text-gray-200 hover:bg-purple-600/30 hover:text-purple-200'
                        : 'bg-gray-900/50 text-gray-500 cursor-not-allowed'
                }`}
                disabled={
                  isPast(new Date(monthDate.getFullYear(), monthDate.getMonth(), c)) || 
                  !!fullyBookedDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`] ||
                  !availableDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`]
                }
              >
                {c}
                {/* Visual markers: blocked (holiday) and occupied (bookings) */}
                {(blockedDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`] || fullyBookedDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`]) && (
                  <span className="absolute top-1 right-1 w-2 h-2 bg-red-400 rounded-full" aria-hidden />
                )}
                {/* Show availability indicator */}
                {availableDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`] && !fullyBookedDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`] && (
                  <span className="absolute bottom-1 left-1 w-2 h-2 bg-emerald-400 rounded-full" aria-hidden />
                )}
              </button>
            )}
          </div>
        ))}
      </div>
      
      <div className="mt-4 text-center">
        <div className="flex items-center justify-center gap-4 text-xs text-gray-400">
          <div className="flex items-center gap-1">
            <span className="w-2 h-2 bg-emerald-400 rounded-full"></span>
            <span>Доступно</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="w-2 h-2 bg-red-400 rounded-full"></span>
            <span>Занято</span>
          </div>
        </div>
      </div>
    </div>
  )
}
