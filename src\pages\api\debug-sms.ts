import { NextApiRequest, NextApiResponse } from 'next';
import { sendSMSViaTwilio } from '@/lib/twilio';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🧪 Debug SMS API called');
    console.log('📱 Request body:', req.body);

  const { phone, message } = req.body as { phone?: string; message?: string };

    if (!phone || !message) {
      return res.status(400).json({ error: 'Phone and message are required' });
    }

    console.log('📤 Sending SMS to:', phone);
    console.log('💬 Message:', message);

    const success = await sendSMSViaTwilio({
      to: phone,
      text: message
    });

    if (success) {
      console.log('✅ SMS sent successfully');
      return res.status(200).json({ success: true, message: 'SMS sent successfully' });
    } else {
      console.log('❌ SMS sending failed');
      return res.status(500).json({ success: false, error: 'SMS sending failed' });
    }

  } catch (error: unknown) {
    const msg = error instanceof Error ? error.message : String(error);
    console.error('💥 Debug SMS API error:', msg);
    return res.status(500).json({ success: false, error: msg });
  }
}
