// pages/api/send-sms.ts
import { NextApiRequest, NextApiResponse } from 'next'
import { sendSMSViaTwilio } from '@/lib/twilio'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  const { phone, text } = req.body as { phone?: string; text?: string }

  if (!phone || !text) {
    return res.status(400).json({ message: 'Missing phone or text' })
  }

  try {
    const success = await sendSMSViaTwilio({ to: phone, text })
    
    if (success) {
      res.status(200).json({ success: true, message: 'SMS sent successfully via <PERSON><PERSON><PERSON>' })
    } else {
      res.status(500).json({ success: false, message: 'Failed to send SMS via Twilio' })
    }
  } catch (error: unknown) {
    const msg = error instanceof Error ? error.message : String(error);
    console.error('Twilio API error:', msg)
    res.status(500).json({ success: false, message: 'SMS service error', error: msg })
  }
}
