-- 001_create_slots_and_modify_bookings.sql
-- Create slots table and extend bookings with slot_id and status

-- 1) Create slots table
CREATE TABLE IF NOT EXISTS slots (
  id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  date date NOT NULL,
  start_time time NOT NULL,
  end_time time NOT NULL,
  capacity int NOT NULL DEFAULT 1,
  created_by text,
  created_at timestamptz DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_slots_date ON slots(date);

-- 2) Alter bookings: add slot_id and status
ALTER TABLE IF EXISTS bookings
  ADD COLUMN IF NOT EXISTS slot_id bigint REFERENCES slots(id) ON DELETE SET NULL,
  ADD COLUMN IF NOT EXISTS status text DEFAULT 'booked';

CREATE INDEX IF NOT EXISTS idx_bookings_slot_id ON bookings(slot_id);
CREATE INDEX IF NOT EXISTS idx_bookings_date ON bookings(date);

-- 3) Optional: schedule_rules table for recurring availability (optional)
CREATE TABLE IF NOT EXISTS schedule_rules (
  id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  weekday int NOT NULL, -- 0=Mon .. 6=Sun
  start_time time NOT NULL,
  end_time time NOT NULL,
  enabled boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);
