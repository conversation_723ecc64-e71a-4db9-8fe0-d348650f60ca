'use client'

import React from 'react'
import ScheduleManager from '@/components/Admin/ScheduleManager'
import BookingsHistory from '@/components/Admin/BookingsHistory'
// ReminderManager removed from admin UI (SMS reminders disabled)

export default function AdminPage() {

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900/10 text-gray-100">
      <div className="max-w-7xl mx-auto p-6">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-light text-transparent bg-clip-text bg-gradient-to-r from-pink-400 via-purple-400 to-indigo-400 mb-3">
            Панель администратора
          </h1>
          <div className="text-lg font-light text-gray-300 mb-2">Управление расписанием и уведомлениями</div>
          <p className="text-sm text-gray-500 max-w-2xl mx-auto">
            Выберите дату в календаре для создания слотов или отметки выходных дней. 
            Настройте рабочие часы, длительность приема и паузы.
          </p>
          
      
        </div>
        
        <ScheduleManager />
        <BookingsHistory />
    
      </div>
    </main>
  )
}
