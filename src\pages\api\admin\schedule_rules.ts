import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_ROLE = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE

type Rule = { 
  id: number
  weekday: number // 0=Monday, 1=Tuesday, ..., 6=Sunday
  start_time: string
  end_time: string
  enabled: boolean
  created_at?: string
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE) {
      console.error('Supabase env vars are missing: SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY')
      return res.status(500).json({ error: 'Supabase is not configured on the server' })
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE)

    if (req.method === 'GET') {
      const { data, error } = await supabase
        .from('schedule_rules')
        .select('*')
        .order('weekday', { ascending: true })
        .order('start_time', { ascending: true })

      if (error) {
        console.error('Supabase select schedule_rules error:', error)
        return res.status(500).json({ error: 'Failed to fetch schedule rules' })
      }

      return res.status(200).json(data || [])
    }

    if (req.method === 'POST') {
      const { weekday, start_time, end_time, enabled = true } = req.body
      
      if (weekday === undefined || !start_time || !end_time) {
        return res.status(400).json({ error: 'Missing required fields: weekday, start_time, end_time' })
      }

      if (weekday < 0 || weekday > 6) {
        return res.status(400).json({ error: 'Invalid weekday. Must be 0-6 (0=Monday, 6=Sunday)' })
      }

      if (start_time >= end_time) {
        return res.status(400).json({ error: 'Start time must be before end time' })
      }

      // Проверяем, нет ли пересекающихся правил для того же дня недели
      const { data: existingRules, error: checkError } = await supabase
        .from('schedule_rules')
        .select('*')
        .eq('weekday', weekday)
        .eq('enabled', true)
        .or(`and(start_time.lte.${end_time},end_time.gt.${start_time})`)

      if (checkError) {
        console.error('Supabase check schedule_rules error:', checkError)
        return res.status(500).json({ error: 'Failed to check existing rules' })
      }

      if (existingRules && existingRules.length > 0) {
        return res.status(409).json({ error: 'Time rule conflicts with existing rule for this weekday' })
      }

      const { data, error } = await supabase
        .from('schedule_rules')
        .insert([{ weekday, start_time, end_time, enabled }])
        .select()
        .single()

      if (error) {
        console.error('Supabase insert schedule_rule error:', error)
        return res.status(500).json({ error: 'Failed to create schedule rule' })
      }

      return res.status(201).json(data)
    }

    if (req.method === 'PUT') {
      const { id } = req.query
      const { weekday, start_time, end_time, enabled } = req.body
      
      if (!id || isNaN(Number(id))) {
        return res.status(400).json({ error: 'Invalid ID' })
      }

      const updateData: Partial<Rule> = {}
      if (weekday !== undefined) updateData.weekday = weekday
      if (start_time !== undefined) updateData.start_time = start_time
      if (end_time !== undefined) updateData.end_time = end_time
      if (enabled !== undefined) updateData.enabled = enabled

      if (Object.keys(updateData).length === 0) {
        return res.status(400).json({ error: 'No fields to update' })
      }

      const { data, error } = await supabase
        .from('schedule_rules')
        .update(updateData)
        .eq('id', Number(id))
        .select()
        .single()

      if (error) {
        console.error('Supabase update schedule_rule error:', error)
        return res.status(500).json({ error: 'Failed to update schedule rule' })
      }

      return res.status(200).json(data)
    }

    if (req.method === 'DELETE') {
      const { id } = req.query
      
      if (!id || isNaN(Number(id))) {
        return res.status(400).json({ error: 'Invalid ID' })
      }

      const { error } = await supabase
        .from('schedule_rules')
        .delete()
        .eq('id', Number(id))

      if (error) {
        console.error('Supabase delete schedule_rule error:', error)
        return res.status(500).json({ error: 'Failed to delete schedule rule' })
      }

      return res.status(200).json({ success: true })
    }

    return res.status(405).json({ error: 'Method not allowed' })
  } catch (error) {
    console.error('Unexpected error in schedule_rules API:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
