type BookingInfo = {
  name: string
  date: string
  time: string
  address?: string
  language?: 'ru' | 'lv'
}

// Central place for SMS templates. Edit messages here to change copy.
export function buildConfirmationSMS(info: BookingInfo) {
  // Helper: format time to short HH:MM (strip seconds if present)
  function formatShortTime(time?: string) {
    if (!time) return '';
    const t = String(time).trim();
    // Match HH:MM at start
    const m = t.match(/^(\d{1,2}:\d{2})/);
    if (m) return m[1].padStart(5, '0');
    // Try ISO time like 2025-09-15T15:00:00
    const iso = t.match(/T(\d{2}:\d{2})/);
    if (iso) return iso[1];
    return t;
  }

  const lang = info.language === 'lv' ? 'lv' : 'ru'
  // Localized address label with emoji
  let addressPart = ''
  if (info.address) {
    addressPart = lang === 'lv' ? `\n📍 Adrese: ${info.address}` : `\n📍 Адрес: ${info.address}`
  }
  
  if (lang === 'lv') {
    // Latvian version
    return `✅ Rezervācija apstiprināta!\n📅 ${info.date} ${formatShortTime(info.time)}\n💁 ${info.name}${addressPart}\nUz tikšanos!\naboutyourskin 🤍`;
  }
  
  // Russian default
  return `✅ Бронирование подтверждено!\n📅 ${info.date} в ${formatShortTime(info.time)}\n💁 ${info.name}${addressPart}\nДо встречи!\naboutyourskin 🤍`;
}

export function buildReminderSMS(info: BookingInfo) {
  // Helper: format time to short HH:MM (strip seconds if present)
  function formatShortTime(time?: string) {
    if (!time) return '';
    const t = String(time).trim();
    const m = t.match(/^(\d{1,2}:\d{2})/);
    if (m) return m[1].padStart(5, '0');
    const iso = t.match(/T(\d{2}:\d{2})/);
    if (iso) return iso[1];
    return t;
  }

  const lang = info.language === 'lv' ? 'lv' : 'ru'
  // Localized address label with emoji
  let addressPart = ''
  if (info.address) {
    addressPart = lang === 'lv' ? `\n📍 Adrese: ${info.address}` : `\n📍 Адрес: ${info.address}`
  }
  
  if (lang === 'lv') {
    return `🔔 Atgādinājums par vizīti!\n📅 Rīt ${info.date} ${formatShortTime(info.time)}\n💁 ${info.name}${addressPart}\n\nUz tikšanos!\n\naboutyourskin 🤍`;
  }
  
  return `🔔 Напоминание о визите!\n📅 Завтра ${info.date} в ${formatShortTime(info.time)}\n💁 ${info.name}${addressPart}\n\nДо встречи!\n\naboutyourskin 🤍`;
}
