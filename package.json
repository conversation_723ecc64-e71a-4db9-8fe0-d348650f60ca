{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.57.4", "@tailwindcss/line-clamp": "^0.4.4", "axios": "^1.12.1", "dotenv": "^17.2.2", "framer-motion": "^11.18.2", "lightningcss": "^1.30.1", "lucide-react": "^0.400.0", "next": "^15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "twilio": "^5.9.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.6", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "^5"}}