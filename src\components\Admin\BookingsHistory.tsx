 'use client'

import { useEffect, useState } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'

// Утилита для форматирования времени в короткий формат HH:MM
function formatShortTime(timeStr?: string): string {
  if (!timeStr) return ''
  const trimmed = timeStr.trim()
  // Если уже в формате HH:MM, возвращаем как есть
  if (/^\d{1,2}:\d{2}$/.test(trimmed)) {
    return trimmed.padStart(5, '0')
  }
  // Если в формате HH:MM:SS, убираем секунды
  const match = trimmed.match(/^(\d{1,2}:\d{2})/)
  if (match) {
    return match[1].padStart(5, '0')
  }
  return trimmed
}

type Booking = {
  id: number
  date: string
  time: string
  name: string
  surname?: string
  email?: string
  phone?: string
  status?: string
  visit_type?: string
  message?: string
  created_at?: string
}

export default function BookingsHistory() {
  // language from context is not needed here; keep hook for future i18n if required
  useLanguage()

  const [month, setMonth] = useState(() => {
    const d = new Date()
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`
  })
  const [showAll, setShowAll] = useState(true)
  const [loading, setLoading] = useState(false)
  const [bookings, setBookings] = useState<Booking[]>([])
  const [error, setError] = useState('')
  const [search, setSearch] = useState('')
  const [page, setPage] = useState(1)
  const [statusFilter, setStatusFilter] = useState('all')
  const [sortField, setSortField] = useState<'date' | 'name' | 'created'>('date')
  const [sortDir, setSortDir] = useState<'asc' | 'desc'>('desc')
  const pageSize = 15

  const fetchBookings = async () => {
    setLoading(true)
    setError('')
    try {
      // Всегда загружаем из админ API
      const res = await fetch('/api/admin/bookings')
      if (!res.ok) throw new Error('Ошибка загрузки данных')
      const data = await res.json()
      const list: Booking[] = Array.isArray(data) ? data : []
      setBookings(list)
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка загрузки';
      setError(errorMessage)
      console.error('Fetch bookings error:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateBookingStatus = async (id: number, status: string) => {
    try {
      const res = await fetch(`/api/admin/bookings/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      })
      if (!res.ok) throw new Error('Ошибка обновления статуса')
      // Обновляем локально
      setBookings(prev => prev.map(b => b.id === id ? { ...b, status } : b))
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка обновления';
      setError(errorMessage)
    }
  }

  useEffect(() => {
    fetchBookings()
  }, [])

  useEffect(() => {
    if (!showAll) {
      fetchBookings()
    }
  }, [month, showAll])

  // Фильтрация и сортировка
  const filtered = bookings.filter(b => {
    // Фильтр по месяцу
    if (!showAll) {
      const bookingMonth = `${new Date(b.date).getFullYear()}-${String(new Date(b.date).getMonth() + 1).padStart(2, '0')}`
      if (bookingMonth !== month) return false
    }
    
    // Фильтр по статусу
    if (statusFilter !== 'all' && b.status !== statusFilter) return false
    
    // Поиск
    if (!search) return true
    const s = search.toLowerCase()
    return (
      (b.name && b.name.toLowerCase().includes(s)) ||
      (b.surname && b.surname.toLowerCase().includes(s)) ||
      (b.email && b.email.toLowerCase().includes(s)) ||
      (b.phone && b.phone.includes(s)) ||
      (b.message && b.message.toLowerCase().includes(s))
    )
  })

  // Сортировка
  const sorted = filtered.slice().sort((a, b) => {
    let compareValue = 0
    
    if (sortField === 'date') {
      const dateA = new Date(`${a.date} ${a.time}`).getTime()
      const dateB = new Date(`${b.date} ${b.time}`).getTime()
      compareValue = dateA - dateB
    } else if (sortField === 'name') {
      const nameA = `${a.name || ''} ${a.surname || ''}`.toLowerCase()
      const nameB = `${b.name || ''} ${b.surname || ''}`.toLowerCase()
      compareValue = nameA.localeCompare(nameB)
    } else if (sortField === 'created') {
      const createdA = new Date(a.created_at || a.date).getTime()
      const createdB = new Date(b.created_at || b.date).getTime()
      compareValue = createdA - createdB
    }
    
    return sortDir === 'asc' ? compareValue : -compareValue
  })

  const totalPages = Math.max(1, Math.ceil(sorted.length / pageSize))
  const pageItems = sorted.slice((page - 1) * pageSize, page * pageSize)

  const getStatusText = (status: string) => {
    switch (status) {
      case 'booked': return 'Забронировано'
      case 'cancelled': return 'Отменено'
      case 'completed': return 'Завершено'
      default: return status || 'Неизвестно'
    }
  }

  return (
    <section className="mt-8 bg-gradient-to-br from-gray-900/50 via-gray-800/50 to-purple-900/20 border border-gray-700/50 rounded-2xl p-6 backdrop-blur-sm shadow-2xl">
      {/* Заголовок и основные действия */}
      <div className="mb-6">
        {/* Mobile-only Add button placed before the heading */}
        <div className="block sm:hidden mb-3">
          <button
            onClick={() => window.location.href = '/admin/new-booking'}
            className="w-full px-4 py-2 bg-gradient-to-r from-emerald-600/40 to-emerald-700/40 hover:from-emerald-700/40 hover:to-emerald-800/40 text-white rounded-lg text-sm transition-all duration-200 transform hover:scale-105 shadow-lg shadow-emerald-500/5"
          >
            ➕ Добавить запись
          </button>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-2xl font-light text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-purple-400 mb-2">История бронирований</h3>
            <p className="text-sm text-gray-400">Управление записями клиентов</p>
          </div>
          <div className="hidden sm:flex items-center gap-3">
            <button 
              onClick={() => window.location.href = '/admin/new-booking'}
              className="px-4 py-2 bg-gradient-to-r from-emerald-600/40 to-emerald-700/40 hover:from-emerald-700/40 hover:to-emerald-800/40 text-white rounded-lg text-sm transition-all duration-200 transform hover:scale-105 shadow-lg shadow-emerald-500/5"
            >
              ➕ Добавить запись
            </button>
            <button 
              onClick={() => { fetchBookings(); setPage(1) }} 
              className="px-4 py-2 bg-gradient-to-r from-purple-600/40 to-purple-700/40 hover:from-purple-700/40 hover:to-purple-800/40 text-white rounded-lg text-sm transition-all duration-200 transform hover:scale-105 shadow-lg shadow-purple-500/5"
            >
              🔄 Обновить
            </button>
          </div>
        </div>
      </div>

      {/* Фильтры и управление */}
      <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 p-3 sm:p-4 rounded-xl border border-gray-600/30 backdrop-blur-sm mb-4">
        {/* Поиск - всегда сверху на мобильных */}
        <div className="mb-3 lg:hidden">
          <input 
            type="text" 
            placeholder="Поиск по имени, email, телефону или сообщению" 
            value={search} 
            onChange={(e) => { setSearch(e.target.value); setPage(1) }} 
            className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-3 py-2 text-sm text-gray-300 placeholder-gray-500 focus:border-purple-500 focus:ring-1 focus:ring-purple-500" 
          />
        </div>

        {/* Основные фильтры */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3">
          {/* Период */}
          <div className="flex flex-col">
            <label className="text-xs font-medium text-gray-300 mb-1.5">Период</label>
            <label className="flex items-center gap-2 text-xs text-gray-300 mb-1.5">
              <input 
                type="checkbox" 
                checked={showAll} 
                onChange={(e) => { setShowAll(e.target.checked); setPage(1) }} 
                className="rounded border-gray-600 bg-gray-700 text-purple-500 focus:ring-purple-500 focus:ring-offset-gray-800 scale-75"
              />
              <span>Все записи</span>
            </label>
            <input 
              type="month" 
              value={month} 
              onChange={(e) => { setMonth(e.target.value); setPage(1) }} 
              className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-2.5 py-1.5 text-sm text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed" 
              disabled={showAll} 
            />
          </div>
          
          {/* Поиск для десктопа */}
          <div className="hidden lg:flex flex-col">
            <label className="text-xs font-medium text-gray-300 mb-1.5">Поиск</label>
            <div className="mb-1.5"></div>
            <input 
              type="text" 
              placeholder="Поиск..." 
              value={search} 
              onChange={(e) => { setSearch(e.target.value); setPage(1) }} 
              className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-2.5 py-1.5 text-sm text-gray-300 placeholder-gray-500 focus:border-purple-500 focus:ring-1 focus:ring-purple-500" 
            />
          </div>

          {/* Статус */}
          <div className="flex flex-col">
            <label className="text-xs font-medium text-gray-300 mb-1.5">Статус</label>
            <div className="hidden lg:block mb-1.5"></div>
            <select 
              value={statusFilter} 
              onChange={(e) => { setStatusFilter(e.target.value); setPage(1) }} 
              className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-2.5 py-1.5 text-sm text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
            >
              <option value="all">Все</option>
              <option value="booked">Забронировано</option>
              <option value="completed">Завершено</option>
              <option value="cancelled">Отменено</option>
            </select>
          </div>

          {/* Сортировка для десктопа */}
          <div className="hidden lg:flex flex-col">
            <label className="text-xs font-medium text-gray-300 mb-1.5">Сортировка</label>
            <div className="mb-1.5"></div>
            <select 
              value={sortField} 
              onChange={(e) => setSortField(e.target.value as 'date' | 'name' | 'created')} 
              className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-2.5 py-1.5 text-sm text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
            >
              <option value="date">По дате</option>
              <option value="name">По имени</option>
              <option value="created">По созданию</option>
            </select>
          </div>

          <div className="hidden lg:flex flex-col">
            <label className="text-xs font-medium text-gray-300 mb-1.5">Порядок</label>
            <div className="mb-1.5"></div>
            <select 
              value={sortDir} 
              onChange={(e) => setSortDir(e.target.value as 'asc' | 'desc')} 
              className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-2.5 py-1.5 text-sm text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
            >
              <option value="desc">Убывание</option>
              <option value="asc">Возрастание</option>
            </select>
          </div>
        </div>

        {/* Сортировка - компактная строка на мобильных */}
        <div className="mt-3 pt-3 border-t border-gray-600/30 lg:hidden">
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="text-xs font-medium text-gray-300 mb-1.5 block">Сортировка</label>
              <select 
                value={sortField} 
                onChange={(e) => setSortField(e.target.value as 'date' | 'name' | 'created')} 
                className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-2.5 py-1.5 text-sm text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
              >
                <option value="date">По дате</option>
                <option value="name">По имени</option>
                <option value="created">По созданию</option>
              </select>
            </div>

            <div>
              <label className="text-xs font-medium text-gray-300 mb-1.5 block">Порядок</label>
              <select 
                value={sortDir} 
                onChange={(e) => setSortDir(e.target.value as 'asc' | 'desc')} 
                className="w-full bg-gray-900/50 border border-gray-600/50 rounded-lg px-2.5 py-1.5 text-sm text-gray-300 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
              >
                <option value="desc">Убывание</option>
                <option value="asc">Возрастание</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-gradient-to-r from-red-900/20 to-red-800/20 border border-red-500/30 rounded-xl text-red-400 text-sm backdrop-blur-sm">
          <div className="flex items-center gap-2">
            <span className="text-red-500">❌</span>
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Статистика */}
      <div className="mb-6 flex items-center justify-between text-sm">
        <div className="text-gray-400">
          Найдено записей: <span className="font-medium text-gray-300">{sorted.length}</span>
          {!showAll && <span className="ml-2">(за {new Date(month + '-01').toLocaleDateString('ru-RU', { month: 'long', year: 'numeric' })})</span>}
        </div>
        <div className="text-gray-400">
          Страница {page} из {totalPages}
        </div>
      </div>

      {/* Таблица для десктопа */}
      <div className="hidden lg:block overflow-x-auto bg-background/20 rounded-xl">
        <div className="bg-gradient-to-r from-gray-800/30 to-gray-700/30 rounded-xl border border-gray-600/30 backdrop-blur-sm overflow-hidden">
          <table className="w-full text-sm">
            <thead className="bg-gradient-to-r from-gray-800/50 to-gray-700/50">
              <tr className="text-left text-gray-300 border-b border-gray-600/30">
                <th className="py-4 px-4 font-medium">Дата и время</th>
                <th className="py-4 px-4 font-medium">Клиент</th>
                <th className="py-4 px-4 font-medium">Контакты</th>
                <th className="py-4 px-4 font-medium">Сообщение</th>
                <th className="py-4 px-4 font-medium min-w-[120px]">Тип визита</th>
                <th className="py-4 px-4 font-medium">Статус</th>
                <th className="py-4 px-4 font-medium">Действия</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={7} className="py-8 text-center">
                    <div className="flex items-center justify-center gap-2 text-gray-400">
                      <div className="animate-spin w-4 h-4 border-2 border-purple-500 border-t-transparent rounded-full"></div>
                      Загрузка...
                    </div>
                  </td>
                </tr>
              ) : sorted.length === 0 ? (
                <tr>
                  <td colSpan={7} className="py-8 text-center text-gray-400">
                    {bookings.length === 0 ? 'Нет записей в базе данных' : 'Нет записей по заданным фильтрам'}
                  </td>
                </tr>
              ) : (
                pageItems.map(b => (
                  <tr key={b.id} className="border-b border-gray-700/30 hover:bg-gray-800/20 transition-all duration-200">
                    <td className="py-4 px-4">
                      <div className="font-medium text-gray-300">{new Date(b.date).toLocaleDateString('ru-RU')}</div>
                      <div className="text-gray-400 text-xs">{formatShortTime(b.time)}</div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="font-medium text-gray-300">{b.name} {b.surname || ''}</div>
                    </td>
                    <td className="py-4 px-4">
                      {b.email && <div className="text-xs text-gray-400 mb-1">📧 {b.email}</div>}
                      {b.phone && <div className="text-xs text-gray-400">📞 {b.phone}</div>}
                    </td>
                    <td className="py-4 px-4 max-w-xs">
                      {b.message ? (
                        <div className="group relative">
                          <div className="text-xs text-gray-300 bg-gray-800/40 rounded-lg px-3 py-2 border border-gray-600/30 cursor-help">
                            <div className="flex items-start gap-2">
                              <span className="text-blue-400 flex-shrink-0">💬</span>
                              <span className="line-clamp-2 break-words overflow-hidden">{b.message}</span>
                            </div>
                          </div>
                          {b.message.length > 50 && (
                            <div className="absolute left-0 top-full mt-2 bg-gray-900 border border-gray-600 rounded-lg p-3 text-xs text-gray-300 shadow-lg z-10 w-80 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                              <div className="font-medium text-blue-400 mb-1">Полное сообщение:</div>
                              <div className="whitespace-pre-wrap break-words">{b.message}</div>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="text-xs text-gray-500 italic">Нет сообщения</div>
                      )}
                    </td>
                    <td className="py-4 px-4 min-w-[120px]">
                      <span className={`px-3 py-1.5 rounded-full text-xs font-medium border whitespace-nowrap ${
                        b.visit_type === 'first' 
                          ? 'bg-emerald-500/20 text-emerald-400 border-emerald-500/30' 
                          : 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                      }`}>
                        {b.visit_type === 'first' ? '🆕 Первый' : '🔄 Повторный'}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <span className={`px-3 py-1.5 rounded-full text-xs font-medium border ${
                        b.status === 'booked' ? 'bg-emerald-500/20 text-emerald-400 border-emerald-500/30' :
                        b.status === 'completed' ? 'bg-blue-500/20 text-blue-400 border-blue-500/30' :
                        b.status === 'cancelled' ? 'bg-red-500/20 text-red-400 border-red-500/30' :
                        'bg-gray-500/20 text-gray-400 border-gray-500/30'
                      }`}>
                        {getStatusText(b.status || '')}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex gap-2">
                        {b.status !== 'completed' && (
                          <button
                            onClick={() => updateBookingStatus(b.id, 'completed')}
                            className="px-3 py-1.5 bg-gradient-to-r from-emerald-600/20 to-emerald-500/20 text-emerald-400 border border-emerald-500/30 rounded-lg text-xs hover:from-emerald-600/30 hover:to-emerald-500/30 hover:border-emerald-400/50 transition-all duration-200 transform hover:scale-105 backdrop-blur-sm"
                            title="Отметить как завершено"
                          >
                            ✅ Завершить
                          </button>
                        )}
                        {b.status === 'booked' && (
                          <button
                            onClick={() => updateBookingStatus(b.id, 'cancelled')}
                            className="px-3 py-1.5 bg-gradient-to-r from-red-600/20 to-red-500/20 text-red-400 border border-red-500/30 rounded-lg text-xs hover:from-red-600/30 hover:to-red-500/30 hover:border-red-400/50 transition-all duration-200 transform hover:scale-105 backdrop-blur-sm"
                            title="Отменить запись"
                          >
                            ❌ Отменить
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Карточки для мобильных устройств */}
      <div className="lg:hidden space-y-4">
        {loading ? (
          <div className="py-8 text-center">
            <div className="flex items-center justify-center gap-2 text-gray-400">
              <div className="animate-spin w-4 h-4 border-2 border-purple-500 border-t-transparent rounded-full"></div>
              Загрузка...
            </div>
          </div>
        ) : sorted.length === 0 ? (
          <div className="py-8 text-center text-gray-400">
            {bookings.length === 0 ? 'Нет записей в базе данных' : 'Нет записей по заданным фильтрам'}
          </div>
        ) : (
          pageItems.map(b => (
            <div key={b.id} className="bg-gradient-to-br from-gray-800/30 to-gray-700/30 rounded-xl border border-gray-600/30 p-4 backdrop-blur-sm overflow-hidden">
              {/* Заголовок карточки */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1 min-w-0 pr-4">
                  <div className="font-medium text-gray-300 text-lg break-words">{b.name} {b.surname || ''}</div>
                  <div className="text-sm text-gray-400">
                    {new Date(b.date).toLocaleDateString('ru-RU')} в {formatShortTime(b.time)}
                  </div>
                </div>
                <div className="flex flex-col gap-2 items-end">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border whitespace-nowrap ${
                    b.visit_type === 'first' 
                      ? 'bg-emerald-500/20 text-emerald-400 border-emerald-500/30' 
                      : 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                  }`}>
                    {b.visit_type === 'first' ? '🆕 Первый' : '🔄 Повторный'}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${
                    b.status === 'booked' ? 'bg-emerald-500/20 text-emerald-400 border-emerald-500/30' :
                    b.status === 'completed' ? 'bg-blue-500/20 text-blue-400 border-blue-500/30' :
                    b.status === 'cancelled' ? 'bg-red-500/20 text-red-400 border-red-500/30' :
                    'bg-gray-500/20 text-gray-400 border-gray-500/30'
                  }`}>
                    {getStatusText(b.status || '')}
                  </span>
                </div>
              </div>

              {/* Контакты */}
              <div className="mb-3">
                {b.email && <div className="text-xs text-gray-400 mb-1 break-all">📧 {b.email}</div>}
                {b.phone && <div className="text-xs text-gray-400 break-all">📞 {b.phone}</div>}
              </div>

              {/* Сообщение */}
              {b.message && (
                <div className="mb-4">
                  <div className="text-xs text-gray-300 bg-gray-800/40 rounded-lg px-3 py-2 border border-gray-600/30">
                    <div className="flex items-start gap-2">
                      <span className="text-blue-400 flex-shrink-0">💬</span>
                      <span className="break-words break-all overflow-hidden">{b.message}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Действия */}
              <div className="flex flex-wrap gap-2">
                {b.status !== 'completed' && (
                  <button
                    onClick={() => updateBookingStatus(b.id, 'completed')}
                    className="flex-1 min-w-[120px] px-3 py-2 bg-gradient-to-r from-emerald-600/20 to-emerald-500/20 text-emerald-400 border border-emerald-500/30 rounded-lg text-xs hover:from-emerald-600/30 hover:to-emerald-500/30 hover:border-emerald-400/50 transition-all duration-200 backdrop-blur-sm text-center"
                  >
                    ✅ Завершить
                  </button>
                )}
                {b.status === 'booked' && (
                  <button
                    onClick={() => updateBookingStatus(b.id, 'cancelled')}
                    className="flex-1 min-w-[120px] px-3 py-2 bg-gradient-to-r from-red-600/20 to-red-500/20 text-red-400 border border-red-500/30 rounded-lg text-xs hover:from-red-600/30 hover:to-red-500/30 hover:border-red-400/50 transition-all duration-200 backdrop-blur-sm text-center"
                  >
                    ❌ Отменить
                  </button>
                )}
              </div>
            </div>
          ))
        )}
      </div>      {/* Пагинация */}
      {totalPages > 1 && (
        <div className="mt-6 flex items-center justify-center gap-2">
          <button 
            disabled={page <= 1} 
            onClick={() => setPage(1)} 
            className="px-3 py-2 rounded-lg border border-gray-600/30 bg-gray-800/30 text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700/40 transition-all duration-200 backdrop-blur-sm"
          >
            ⏮️
          </button>
          <button 
            disabled={page <= 1} 
            onClick={() => setPage(p => Math.max(1, p - 1))} 
            className="px-3 py-2 rounded-lg border border-gray-600/30 bg-gray-800/30 text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700/40 transition-all duration-200 backdrop-blur-sm"
          >
            ⬅️ Назад
          </button>
          <div className="px-4 py-2 rounded-lg bg-gradient-to-r from-purple-600/40 to-purple-700/40 text-white font-medium shadow-lg shadow-purple-500/5">
            {page} / {totalPages}
          </div>
          <button 
            disabled={page >= totalPages} 
            onClick={() => setPage(p => Math.min(totalPages, p + 1))} 
            className="px-3 py-2 rounded-lg border border-gray-600/30 bg-gray-800/30 text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700/40 transition-all duration-200 backdrop-blur-sm"
          >
            Вперед ➡️
          </button>
          <button 
            disabled={page >= totalPages} 
            onClick={() => setPage(totalPages)} 
            className="px-3 py-2 rounded-lg border border-gray-600/30 bg-gray-800/30 text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700/40 transition-all duration-200 backdrop-blur-sm"
          >
            ⏭️
          </button>
        </div>
      )}
    </section>
  )
}
