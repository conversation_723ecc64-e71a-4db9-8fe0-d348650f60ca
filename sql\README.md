# Миграции базы данных

В проекте используется Supabase в качестве базы данных. Миграции находятся в папке `sql/migrations/`.

## Необходимые миграции для работы с расписанием

### 1. Создание таблиц слотов и обновление букингов
```sql
-- Файл: sql/migrations/001_create_slots_and_modify_bookings.sql
-- Выполните этот SQL в панели администратора Supabase
```

### 2. Создание таблицы блокировок
```sql
-- Файл: sql/migrations/002_create_blocks_table.sql
-- Выполните этот SQL в панели администратора Supabase
```

### 3. Создание таблицы перерывов
```sql
-- Файл: sql/migrations/003_create_breaks_table.sql
-- Выполните этот SQL в панели администратора Supabase
```

## Как выполнить миграции

1. Зайдите в панель администратора Supabase
2. Перейдите в раздел "SQL Editor"
3. Скопируйте содержимое файла миграции
4. Вставьте и выполните SQL код
5. Повторите для всех необходимых миграций

## Структура таблиц

### slots
- `id` - уникальный идентификатор слота
- `date` - дата слота (YYYY-MM-DD)
- `start_time` - время начала (HH:MM)
- `end_time` - время окончания (HH:MM)
- `capacity` - вместимость слота (по умолчанию 1)
- `created_by` - кто создал слот
- `created_at` - когда создан слот

### blocks
- `id` - уникальный идентификатор блокировки
- `date` - заблокированная дата (YYYY-MM-DD)
- `reason` - причина блокировки (опционально)
- `created_by` - кто создал блокировку
- `created_at` - когда создана блокировка

### breaks
- `id` - уникальный идентификатор перерыва
- `date` - дата перерыва (YYYY-MM-DD)
- `start_time` - время начала перерыва (HH:MM)
- `end_time` - время окончания перерыва (HH:MM)
- `reason` - причина перерыва (по умолчанию 'Перерыв')
- `created_by` - кто создал перерыв
- `created_at` - когда создан перерыв

### schedule_rules
- `id` - уникальный идентификатор правила
- `weekday` - день недели (0=Понедельник, 6=Воскресенье)
- `start_time` - время начала работы (HH:MM)
- `end_time` - время окончания работы (HH:MM)
- `enabled` - активно ли правило
- `created_at` - когда создано правило

### bookings (обновлено)
- добавлены поля:
  - `slot_id` - ссылка на слот
  - `status` - статус бронирования (по умолчанию 'booked')

## Функциональность админ панели

### Управление слотами
- Создание временных слотов на выбранную дату
- Настройка рабочих часов и длительности слотов
- Автоматический учет перерывов при генерации слотов
- Предпросмотр слотов перед созданием

### Управление блокировками
- Блокировка целых дней (выходные)
- Отображение заблокированных дат в календаре

### Управление перерывами
- Создание перерывов на конкретные даты и время (обед, технические перерывы и т.д.)
- Автоматический учет перерывов при создании слотов
- Отображение существующих перерывов
- Визуальные индикаторы в календаре

### Индикаторы в календаре
- 🟢 Зеленая точка - есть созданные слоты
- 🟠 Оранжевая точка - есть перерывы
- 🔴 Красная точка - день заблокирован
