 'use client';

 import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
 import { useLanguage } from '@/contexts/LanguageContext';
import { Calendar, Clock, Mail, Phone, User, MessageSquare, Send } from 'lucide-react';
import Image from 'next/image';

interface SlotData {
  date: string;
  start_time: string;
}

interface BlockData {
  date: string;
}

// Утилита для форматирования времени в короткий формат HH:MM
function formatShortTime(timeStr?: string): string {
  if (!timeStr) return ''
  const trimmed = timeStr.trim()
  // Если уже в формате HH:MM, возвращаем как есть
  if (/^\d{1,2}:\d{2}$/.test(trimmed)) {
    return trimmed.padStart(5, '0')
  }
  // Если в формате HH:MM:SS, убираем секунды
  const match = trimmed.match(/^(\d{1,2}:\d{2})/)
  if (match) {
    return match[1].padStart(5, '0')
  }
  return trimmed
}

 export default function BookingSection() {
  const { t, language } = useLanguage();
  const locale = language === 'ru' ? 'ru-RU' : 'lv-LV';

   const [formData, setFormData] = useState({
     name: '',
     surname: '',
     email: '',
     phone: '',
     countryCode: '+371', // По умолчанию Латвия
     visitType: '',
     date: '',
     time: '',
     message: '',
   });

   const [rulesAccepted, setRulesAccepted] = useState(false);
   const [showVisitTypeModal, setShowVisitTypeModal] = useState(false);
   const [showTimeModal, setShowTimeModal] = useState(false);
  const [showDateModal, setShowDateModal] = useState(false);
  const [bookedTimes, setBookedTimes] = useState<string[]>([]);
  // visitType inline error removed; we use a single global message `serverError`
  const [submitting, setSubmitting] = useState(false);
  const [serverError, setServerError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  // How long the success banner stays visible (ms). Change as needed.
  const SUCCESS_TIMEOUT = 4500;

  // Auto-hide success banner after SUCCESS_TIMEOUT ms
  useEffect(() => {
    if (!successMessage) return;
    const id = setTimeout(() => setSuccessMessage(''), SUCCESS_TIMEOUT);
    return () => clearTimeout(id);
  }, [successMessage]);

  // Available times for the currently selected date come from admin-created slots
  const [timeSlots, setTimeSlots] = useState<string[]>([]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target as HTMLInputElement | HTMLSelectElement;
     if (name === 'rulesAccepted') {
       const checked = (e.target as HTMLInputElement).checked;
       setRulesAccepted(checked);
       if (checked) setServerError(''); // Clear error when checkbox is checked
       return;
     }
     setFormData((s) => ({ ...s, [name]: value }));
   };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setServerError('');
    // visitType must be selected — show global message and open modal
    if (!formData.visitType) {
      setShowVisitTypeModal(true);
      setServerError(t('fillRequired'));
      return;
    }

    // basic validation: date and time — show single global message if missing
    if (!formData.date || !formData.time) {
      setServerError(t('fillRequired'));
      return;
    }

    // Проверяем номер телефона
    if (!formData.phone || formData.phone.trim().length < 4) {
      setServerError('Введите корректный номер телефона');
      return;
    }

    // rules checkbox must be accepted
    if (!rulesAccepted) {
      setServerError(t('acceptRulesRequired'));
      return;
    }

    setSubmitting(true);
    try {
      // Формируем финальный номер телефона
      let finalPhone = '';
      
      // Если пользователь ввёл полный номер с кодом страны
      if (formData.phone.startsWith('+')) {
        finalPhone = formData.phone; // Используем как есть
      } else {
        // Если ввёл только номер без кода страны, добавляем выбранный код
        finalPhone = formData.countryCode + formData.phone.replace(/^\+/, '');
      }
      
      console.log('📱 Phone formatting:', {
        original: formData.phone,
        countryCode: formData.countryCode,
        final: finalPhone,
        startsWithPlus: formData.phone.startsWith('+')
      });

      const res = await fetch('/api/bookings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          ...formData, 
          phone: finalPhone, // Убираем только + в начале номера
          language: language // Передаём текущий язык интерфейса
        }),
      });

      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        setServerError(data?.error || 'Server error');
        alert(data?.error || 'Server error');
        return;
      }

  // success — show inline success banner instead of alert
  setFormData({ name: '', surname: '', email: '', phone: '', countryCode: '+371', visitType: '', date: '', time: '', message: '' });
  setRulesAccepted(false);
  setServerError('');
  setSuccessMessage(t('bookingSuccess'));
    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : String(err);
      setServerError(message);
      alert(message);
    } finally {
      setSubmitting(false);
    }
  };

  // Fetch booked times and admin slots for the selected date
  useEffect(() => {
    const fetchForDate = async () => {
      if (!formData.date) {
        setBookedTimes([]);
        setTimeSlots([]);
        return;
      }
      try {
        // bookings for this date
        const bookedRes = await fetch(`/api/bookings?date=${encodeURIComponent(formData.date)}`)
        const bookedData = bookedRes.ok ? await bookedRes.json().catch(() => ({})) : {}
        setBookedTimes(Array.isArray(bookedData.times) ? bookedData.times : [])

        // admin slots for this date (fetch all and filter client-side)
        const slotsRes = await fetch('/api/admin/slots')
        if (!slotsRes.ok) {
          setTimeSlots([])
        } else {
          const allSlots = await slotsRes.json()
          const times: string[] = (Array.isArray(allSlots) ? allSlots : [])
            .filter((s: SlotData) => s.date === formData.date)
            .map((s: SlotData) => s.start_time)
            .filter((v: string, i: number, a: string[]) => typeof v === 'string' && a.indexOf(v) === i)
            .sort((a, b) => a.localeCompare(b))
          setTimeSlots(times)
        }
      } catch {
        setBookedTimes([])
        setTimeSlots([])
      }
    }
    fetchForDate()
  }, [formData.date])

   return (
     <section id="booking" className="py-20 px-4 sm:px-6 lg:px-8">
       <div className="max-w-4xl mx-auto">
         <div className="text-center mb-16">
           <motion.div
             className="inline-block px-4 py-2 bg-accent/10 border border-accent/20 rounded-full text-accent text-sm font-medium mb-6"
             initial={{ opacity: 0, scale: 0.8 }}
             whileInView={{ opacity: 1, scale: 1 }}
             transition={{ duration: 0.6 }}
             viewport={{ once: true }}
           >
             {t('booking')}
           </motion.div>

          {/* bookingTitle removed to avoid duplication with the badge (t('booking')) */}

           <motion.p
             className="text-lg text-foreground/80 max-w-2xl mx-auto"
             initial={{ opacity: 0, y: 20 }}
             whileInView={{ opacity: 1, y: 0 }}
             transition={{ duration: 0.8, delay: 0.4 }}
             viewport={{ once: true }}
           >
             {t('bookingSubtitle')}
           </motion.p>
         </div>

         <motion.div
           className="bg-gradient-to-br from-background to-muted/30 p-8 rounded-3xl border border-border/50 backdrop-blur-sm"
           initial={{ opacity: 0, y: 50 }}
           whileInView={{ opacity: 1, y: 0 }}
           transition={{ duration: 0.8, delay: 0.6 }}
           viewport={{ once: true }}
         >
           <form onSubmit={handleSubmit} className="space-y-6">
             <div className="grid md:grid-cols-3 gap-6">
               <div className="space-y-2">
                 <label className="flex items-center text-sm font-medium text-foreground/80 mb-2">
                   <User className="w-4 h-4 mr-2 text-accent" />
                   {t('name')} *
                 </label>
                 <input
                   name="name"
                   value={formData.name}
                   onChange={handleChange}
                   required
                   className="w-full px-4 py-3 bg-background/50 border border-border/50 rounded-xl focus:border-accent focus:outline-none transition-colors duration-300 text-foreground"
                 />
               </div>

               <div className="space-y-2">
                 <label className="flex items-center text-sm font-medium text-foreground/80 mb-2">
                   <User className="w-4 h-4 mr-2 text-accent" />
                   {t('surname') || 'Uzvārds'} *
                 </label>
                 <input
                   name="surname"
                   value={formData.surname}
                   onChange={handleChange}
                   required
                   className="w-full px-4 py-3 bg-background/50 border border-border/50 rounded-xl focus:border-accent focus:outline-none transition-colors duration-300 text-foreground"
                 />
               </div>

               <div className="space-y-2">
                 <label className="flex items-center text-sm font-medium text-foreground/80 mb-2">
                   <Mail className="w-4 h-4 mr-2 text-accent" />
                   {t('email')} *
                 </label>
                 <input
                   name="email"
                   value={formData.email}
                   onChange={handleChange}
                   required
                   className="w-full px-4 py-3 bg-background/50 border border-border/50 rounded-xl focus:border-accent focus:outline-none transition-colors duration-300 text-foreground"
                 />
               </div>
             </div>

             <div className="flex flex-col md:flex-row gap-4 w-full items-end">
               <div className="flex-1 flex flex-col">
                 <label className="flex items-center text-sm font-medium text-foreground/80 mb-2">
                   <Phone className="w-4 h-4 mr-2 text-accent" />
                   {t('phone')} *
                 </label>
                 <div className="flex gap-2">
                   <select
                     name="countryCode"
                     value={formData.countryCode}
                     onChange={handleChange}
                     className="px-3 py-3 bg-background/50 border border-border/50 rounded-xl focus:border-accent focus:outline-none transition-colors duration-300 text-foreground h-[48px] w-24"
                   >
                     <option value="+371">🇱🇻 +371</option>
                     <option value="+372">🇪🇪 +372</option>
                     <option value="+370">🇱🇹 +370</option>
                     <option value="+7">🇷🇺 +7</option>
                     <option value="+1">🇺🇸 +1</option>
                     <option value="+49">🇩🇪 +49</option>
                     <option value="+46">🇸🇪 +46</option>
                   </select>
                   <input
                     name="phone"
                     value={formData.phone}
                     onChange={handleChange}
                     placeholder="12345678"
                     required
                     className="flex-1 px-4 py-3 bg-background/50 border border-border/50 rounded-xl focus:border-accent focus:outline-none transition-colors duration-300 text-foreground h-[48px]"
                   />
                 </div>
               </div>

               <div className="flex-1 flex flex-col justify-end">
                 <label className="invisible mb-2">visit type</label>
                 <button
                   type="button"
                   className="w-full px-4 py-3 bg-background border border-accent/40 rounded-xl focus:border-accent focus:outline-none transition-colors duration-300 text-foreground flex items-center justify-between h-[48px]"
                   onClick={() => setShowVisitTypeModal(true)}
                 >
                   <span>{formData.visitType ? (formData.visitType === 'first' ? t('visitTypeFirst') : t('visitTypeRepeat')) : t('visitType')}</span>
                   <span className="text-accent">▼</span>
                 </button>
                {/* No inline per-field error - global message shown above submit button */}
               </div>
             </div>

             <div className="grid md:grid-cols-2 gap-6">
               <div className="space-y-2">
                 <label className="flex items-center text-sm font-medium text-foreground/80 mb-2">
                   <Calendar className="w-4 h-4 mr-2 text-accent" />
                   {t('date')} *
                 </label>
                 <button
                   type="button"
                   className="w-full px-4 py-3 bg-background/50 border border-border/50 rounded-xl focus:border-accent focus:outline-none transition-colors duration-300 text-foreground flex items-center justify-between h-[48px]"
                   onClick={() => setShowDateModal(true)}
                 >
                   <span>{formData.date ? new Date(formData.date).toLocaleDateString(locale) : t('selectOption')}</span>
                   <svg className="w-5 h-5 text-accent ml-2" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
                 </button>
               </div>

               <div className="space-y-2">
                 <label className="flex items-center text-sm font-medium text-foreground/80 mb-2">
                   <Clock className="w-4 h-4 mr-2 text-accent" />
                   {t('time')} *
                 </label>
                 <button
                   type="button"
                   className="w-full px-4 py-3 bg-background/50 border border-border/50 rounded-xl focus:border-accent focus:outline-none transition-colors duration-300 text-foreground flex items-center justify-between cursor-pointer"
                   onClick={() => setShowTimeModal(true)}
                 >
                   <span>{formData.time ? formData.time : t('selectOption')}</span>
                   <svg className="w-5 h-5 text-accent ml-2" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
                 </button>
               </div>
             </div>

             <div className="space-y-2">
               <label className="flex items-center text-sm font-medium text-foreground/80 mb-2">
                 <MessageSquare className="w-4 h-4 mr-2 text-accent" />
                 {t('message')}
               </label>
                <textarea
                 name="message"
                 value={formData.message}
                 onChange={handleChange}
                 rows={4}
                  className="w-full px-4 py-3 bg-background/50 border border-border/50 rounded-xl focus:border-accent focus:outline-none transition-colors duration-300 text-foreground resize-none"
                  placeholder={t('messagePlaceholder')}
               />
             </div>

             <div className="flex items-center space-x-2">
               <input
                 type="checkbox"
                 name="rulesAccepted"
                 value="accepted"
                 checked={rulesAccepted}
                 onChange={handleChange}
                 className="accent-accent w-5 h-5 rounded focus:ring-2 focus:ring-accent"
               />
               <label htmlFor="rulesAccepted" className="text-sm text-foreground/80">
                 {t('acceptRules')}
               </label>
             </div>

            {/* Global messages above the submit button */}
            {successMessage && (
              <div className="rounded-md bg-accent/10 border border-accent/30 p-3 text-accent mb-2 flex items-center justify-between">
                <div className="font-medium">{successMessage}</div>
                <button type="button" onClick={() => setSuccessMessage('')} className="text-accent font-medium">✕</button>
              </div>
            )}
            {serverError && <p className="text-sm text-red-400 mt-2">{serverError}</p>}

             <motion.button
               type="submit"
               className="w-full bg-accent/70 text-background py-4 rounded-xl font-medium text-lg hover:bg-accent/60 transition-all duration-300 shadow-lg hover:shadow-accent/5 flex items-center justify-center space-x-2"
               whileHover={{ scale: 1.02, y: -2 }}
               whileTap={{ scale: 0.98 }}
              disabled={submitting}
              aria-busy={submitting}
             >
               <Send className="w-5 h-5" />
               <span>{t('submit')}</span>
             </motion.button>
           </form>

           <div className="mt-8 pt-8 border-t border-border/30">
             <div className="text-center">
              <h4 className="text-lg font-medium text-foreground mb-4">{t('callUs')}</h4>
               <div className="flex flex-col sm:flex-row items-center justify-center gap-4 text-foreground/80">
                 {/* compute display phone from translations (t('phoneValue') is stored without spaces) */}
                 {(() => {
                   const digits = t('phoneValue').replace(/\D/g, '');
                   const display = digits.length > 3 ? `+${digits.slice(0,3)} ${digits.slice(3)}` : `+${digits}`;
                   return (
                     <>
                       <a href={`tel:+${digits}`} className="flex items-center space-x-2 hover:text-accent transition-colors">
                         <Phone className="w-5 h-5" />
                         <span>{display}</span>
                       </a>

                       <a href={`https://wa.me/${digits}`} className="flex items-center space-x-2 hover:text-accent transition-colors">
                         <div className="w-8 flex items-center justify-center">
                           <Image src={encodeURI('/WhatsApp-Logo in Schwarz-Weiß.png')} alt="WhatsApp" width={32} height={32} className="w-8 h-8 object-contain" />
                         </div>
                         <span>{display}</span>
                       </a>
                     </>
                   );
                 })()}

                 <a href={`mailto:${t('emailValue')}`} className="flex items-center space-x-2 hover:text-accent transition-colors">
                   <Mail className="w-5 h-5" />
                   <span>{t('emailValue')}</span>
                 </a>
               </div>
             </div>
           </div>

           <AnimatePresence>
            {showVisitTypeModal && (
              <motion.div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
                <motion.div className="bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900/20 rounded-2xl shadow-2xl border border-gray-700/50 p-6 w-full max-w-sm mx-4 backdrop-blur-sm" initial={{ scale: 0.9, opacity: 0, y: 20 }} animate={{ scale: 1, opacity: 1, y: 0 }} exit={{ scale: 0.9, opacity: 0, y: 20 }} transition={{ duration: 0.2, ease: "easeOut" }}>
                   <div className="flex items-center justify-between mb-4">
                     <h3 className="text-xl font-semibold text-gray-100">{t('visitType')}</h3>
                     <button onClick={() => setShowVisitTypeModal(false)} className="text-gray-400 hover:text-gray-200 transition-colors">
                       <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                       </svg>
                     </button>
                   </div>
                   <div className="space-y-3">
                     <button type="button" className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 text-sm font-medium ${formData.visitType === 'first' ? 'border-pink-500 bg-gradient-to-r from-pink-500/20 to-purple-500/20 text-pink-200 ring-2 ring-pink-500/30' : 'border-gray-600/50 bg-gray-800/50 text-gray-300 hover:bg-gray-700/50 hover:border-gray-500/50'}`} onClick={() => { setFormData({ ...formData, visitType: 'first' }); setShowVisitTypeModal(false); }}>
                       {t('visitTypeFirst')}
                     </button>
                     <button type="button" className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 text-sm font-medium ${formData.visitType === 'repeat' ? 'border-pink-500 bg-gradient-to-r from-pink-500/20 to-purple-500/20 text-pink-200 ring-2 ring-pink-500/30' : 'border-gray-600/50 bg-gray-800/50 text-gray-300 hover:bg-gray-700/50 hover:border-gray-500/50'}`} onClick={() => { setFormData({ ...formData, visitType: 'repeat' }); setShowVisitTypeModal(false); }}>
                       {t('visitTypeRepeat')}
                     </button>
                   </div>
                </motion.div>
              </motion.div>
            )}
           </AnimatePresence>

           <AnimatePresence>
            {showTimeModal && (
              <motion.div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
                <motion.div className="bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900/20 rounded-2xl shadow-2xl border border-gray-700/50 p-6 w-full max-w-md mx-4 backdrop-blur-sm" initial={{ scale: 0.9, opacity: 0, y: 20 }} animate={{ scale: 1, opacity: 1, y: 0 }} exit={{ scale: 0.9, opacity: 0, y: 20 }} transition={{ duration: 0.2, ease: "easeOut" }}>
                   <div className="flex items-center justify-between mb-4">
                     <h3 className="text-xl font-semibold text-gray-100">{t('time')}</h3>
                     <button onClick={() => setShowTimeModal(false)} className="text-gray-400 hover:text-gray-200 transition-colors">
                       <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                       </svg>
                     </button>
                   </div>
                   <div className="grid grid-cols-2 gap-3">
                    {timeSlots.length === 0 && (
                      <div className="text-sm text-gray-400 col-span-2 text-center py-4">{t('noAvailableSlots') || 'Нет доступных слотов'}</div>
                    )}
                    {timeSlots.map((time) => {
                       const isBooked = bookedTimes.includes(time);
                       const isSelected = formData.time === time;
                       return (
                         <button
                           key={time}
                           type="button"
                           disabled={isBooked}
                           className={`px-3 py-3 rounded-lg border transition-all duration-200 text-sm font-medium ${
                             isBooked 
                               ? 'border-red-500/50 bg-red-900/20 text-red-400 cursor-not-allowed opacity-50' 
                               : isSelected
                                 ? 'border-pink-500 bg-gradient-to-r from-pink-500/20 to-purple-500/20 text-pink-200 ring-2 ring-pink-500/30'
                                 : 'border-gray-600/50 bg-gray-800/50 text-gray-300 hover:bg-gray-700/50 hover:border-gray-500/50 hover:scale-105'
                           }`}
                           onClick={() => {
                             if (isBooked) return;
                             setFormData({ ...formData, time });
                             setShowTimeModal(false);
                           }}
                         >
                           {formatShortTime(time)}
                         </button>
                       );
                     })}
                   </div>
                </motion.div>
              </motion.div>
            )}
           </AnimatePresence>

          <AnimatePresence>
            {showDateModal && (
              <motion.div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
                <motion.div className="bg-background rounded-2xl shadow-2xl border border-accent/30 p-4 w-full max-w-sm mx-auto flex flex-col gap-3" initial={{ scale: 0.96, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} exit={{ scale: 0.96, opacity: 0 }} transition={{ duration: 0.18 }}>
                  <CalendarModal
                    value={formData.date}
                    locale={locale}
                    onSelect={(iso) => {
                      setFormData({ ...formData, date: iso });
                      setShowDateModal(false);
                    }}
                    onClose={() => setShowDateModal(false)}
                  />
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
         </motion.div>
       </div>
     </section>
   );
 }

// Small in-file calendar modal to keep UI consistent with site styles.
function CalendarModal({ value, locale, onSelect, onClose }: { value?: string; locale?: string; onSelect: (iso: string) => void; onClose: () => void }) {
  const today = new Date();
  const start = value ? new Date(value) : today;
  const { t } = useLanguage();
  const [monthDate, setMonthDate] = useState(new Date(start.getFullYear(), start.getMonth(), 1));
  const [fullyBookedDates, setFullyBookedDates] = useState<Record<string, boolean>>({});
  // blockedDates marks admin holidays/blocks
  const [blockedDates, setBlockedDates] = useState<Record<string, boolean>>({});
  // availableDates denotes dates that have at least one admin slot and are not blocked/fully booked
  const [availableDates, setAvailableDates] = useState<Record<string, boolean>>({});

  // startDay: 0(Sun)..6(Sat) -> convert to Monday-first index 0(Mon)..6(Sun)
  const startDayRaw = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1).getDay();
  const startDay = (startDayRaw + 6) % 7;
  const daysInMonth = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0).getDate();

  const prevMonth = () => setMonthDate(new Date(monthDate.getFullYear(), monthDate.getMonth() - 1, 1));
  const nextMonth = () => setMonthDate(new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 1));

  const isPast = (d: Date) => {
    const dd = new Date(d.getFullYear(), d.getMonth(), d.getDate());
    const t = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    return dd < t;
  };

  const onPick = (day: number) => {
    const d = new Date(monthDate.getFullYear(), monthDate.getMonth(), day);
    if (isPast(d)) return;
  // Format as local YYYY-MM-DD to avoid timezone shifts from toISOString()
  const iso = `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
  if (!availableDates[iso] || fullyBookedDates[iso]) return;
  onSelect(iso);
  };

  const monthLabel = monthDate.toLocaleString(locale || undefined, { month: 'long', year: 'numeric' });

  const weekdayShort = (i: number) => {
    // i is 0..6 where 0 -> Monday
    if (locale && locale.startsWith('lv')) {
      // Latvian two-letter abbreviations Monday..Sunday
      const lv = ['Pr', 'Ot', 'Tr', 'Ct', 'Pk', 'Se', 'Sv'];
      return lv[i] || '';
    }
    // Use a base date that is Monday (1970-01-05) and offset by i
    return new Date(1970, 0, 5 + i).toLocaleString(locale || undefined, { weekday: 'short' });
  };

  const cells: Array<null | number> = [];
  for (let i = 0; i < startDay; i++) cells.push(null);
  for (let d = 1; d <= daysInMonth; d++) cells.push(d);

  // Fetch admin slots, blocks and booking counts for the month to compute available dates
  useEffect(() => {
    const fetchMonth = async () => {
      const y = monthDate.getFullYear();
      const m = String(monthDate.getMonth() + 1).padStart(2, '0');
      const month = `${y}-${m}`;
      try {
        const [bookingsRes, slotsRes, blocksRes] = await Promise.all([
          fetch(`/api/bookings?month=${month}`).catch(() => null),
          fetch('/api/admin/slots').catch(() => null),
          fetch('/api/admin/blocks').catch(() => null),
        ])

        const bookingsData = bookingsRes && bookingsRes.ok ? await bookingsRes.json().catch(() => ({})) : {}
        const rawCounts: Record<string, number> = bookingsData.counts || {}

        const allSlots = slotsRes && slotsRes.ok ? await slotsRes.json().catch(() => []) : []
        const allBlocks = blocksRes && blocksRes.ok ? await blocksRes.json().catch(() => []) : []

        // helper to normalize possible ISO timestamps to YYYY-MM-DD
        const norm = (val: string | number | null | undefined) => {
          if (!val && val !== 0) return ''
          const s = String(val)
          if (s.includes('T')) return s.split('T')[0]
          return s
        }

        const slotsByDate: Record<string, number> = {}
        ;(Array.isArray(allSlots) ? allSlots : []).forEach((s: SlotData) => {
          const d = norm(s.date)
          if (!d) return
          slotsByDate[d] = (slotsByDate[d] || 0) + 1
        })

        const blocked: Record<string, boolean> = {}
        ;(Array.isArray(allBlocks) ? allBlocks : []).forEach((b: BlockData) => {
          const d = norm(b.date)
          if (d) blocked[d] = true
        })

        // normalize counts keys too
        const counts: Record<string, number> = {}
        Object.keys(rawCounts || {}).forEach((k) => {
          const nk = norm(k)
          counts[nk] = rawCounts[k]
        })

        // Compute fully-booked and available for every day of the month.
        const fully: Record<string, boolean> = {}
        const avail: Record<string, boolean> = {}
        // daysInMonth is available in outer scope and represents current month length
        for (let day = 1; day <= daysInMonth; day++) {
          const key = `${y}-${m}-${String(day).padStart(2, '0')}`
          const slotCount = slotsByDate[key] || 0
          const booked = counts[key] || 0
          const isBlocked = !!blocked[key]
          // Treat days with zero admin slots as occupied/fully-booked per request
          const isFully = isBlocked || slotCount === 0 || (slotCount > 0 && booked >= slotCount)
          fully[key] = isFully
          avail[key] = slotCount > 0 && !isBlocked && !isFully
        }

        // bookingsByDate and blockedDates are used only for visuals: show a small dot for occupied days
        setFullyBookedDates(fully)
        setBlockedDates(blocked)
        setAvailableDates(avail)
      } catch {
        setFullyBookedDates({})
        setBlockedDates({})
        setAvailableDates({})
      }
    }
    fetchMonth()
  }, [monthDate, daysInMonth]);

  return (
    <div>
      <div className="flex items-center justify-between">
        <button type="button" onClick={prevMonth} className="text-accent px-2 py-1">◀</button>
        <div className="text-foreground font-medium">{monthLabel}</div>
        <button type="button" onClick={nextMonth} className="text-accent px-2 py-1">▶</button>
      </div>
      <div className="grid grid-cols-7 gap-1 text-center mt-3 text-sm text-foreground/70">
        {[0,1,2,3,4,5,6].map((i) => (
          <div key={i} className="font-medium">{weekdayShort(i)}</div>
        ))}
      </div>
      <div className="grid grid-cols-7 gap-2 mt-2">
        {cells.map((c, i) => (
          <div key={i}>
            {c === null ? (
              <div className="h-8" />
            ) : (
                <button
                  type="button"
                  onClick={() => onPick(c)}
                  title={fullyBookedDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`] ? t('dateFullyBooked') : undefined}
                  className={`w-full h-8 rounded-md text-sm relative flex items-center justify-center ${isPast(new Date(monthDate.getFullYear(), monthDate.getMonth(), c)) ? 'text-foreground/40 bg-background/20 cursor-not-allowed' : fullyBookedDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`] ? 'bg-background/60 text-foreground/60 cursor-not-allowed' : 'bg-background/60 text-foreground hover:bg-accent/10'}`}
                  disabled={isPast(new Date(monthDate.getFullYear(), monthDate.getMonth(), c)) || !!fullyBookedDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`]}
                >
                  {c}
                  {/* Visual markers: blocked (holiday) and occupied (bookings) */}
                  {/* show red marker only for admin-blocked or fully booked dates */}
                  {(blockedDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`] || fullyBookedDates[`${monthDate.getFullYear()}-${String(monthDate.getMonth()+1).padStart(2,'0')}-${String(c).padStart(2,'0')}`]) && (
                    <span className="absolute top-1 right-1 w-2 h-2 bg-red-400 rounded-full" aria-hidden />
                  )}
                </button>
            )}
          </div>
        ))}
      </div>
  {/* debug removed */}
      <div className="mt-3 text-right">
        <button type="button" onClick={onClose} className="text-sm text-accent underline">{t('close')}</button>
      </div>
    </div>
  );
}