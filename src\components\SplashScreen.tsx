'use client';

import { motion, useAnimation } from 'framer-motion';
import { useEffect, useState, useRef } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

interface SplashScreenProps {
  onComplete: () => void;
}

export default function SplashScreen({ onComplete }: SplashScreenProps) {
  const controls = useAnimation();
  const [isVisible, setIsVisible] = useState(true);
  const [isMounted, setIsMounted] = useState(false);
  const svgRef = useRef<SVGSVGElement>(null);

  const { t } = useLanguage();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted || !svgRef.current) return;

    const animateSequence = async () => {
      // Wait a bit before starting
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Get all paths in the SVG
      const paths = svgRef.current!.querySelectorAll('path');
      
      // Start container animation
      await controls.start("visible");
      
      // Animate each path with progressive stroke-draw effect
      paths.forEach((path, index) => {
        const pathLength = path.getTotalLength();
        
        // Set initial state for stroke animation
        path.style.strokeDasharray = `${pathLength}`;
        path.style.strokeDashoffset = `${pathLength}`;
        path.style.stroke = '#f6f6f6';
        path.style.strokeWidth = '1.5';
        path.style.fill = 'transparent';
        
        // Start the stroke animation with stagger
        setTimeout(() => {
          path.animate([
            { strokeDashoffset: pathLength },
            { strokeDashoffset: 0 }
          ], {
            duration: 1000,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
            fill: 'forwards'
          }).addEventListener('finish', () => {
            // Fill animation after stroke completes
            path.animate([
              { fill: 'transparent', stroke: '#f6f6f6' },
              { fill: '#f6f6f6', stroke: '#f6f6f6' }
            ], {
              duration: 600,
              easing: 'ease-out',
              fill: 'forwards'
            });
          });
        }, index * 120); // Stagger each path by 120ms
      });
      
      // Hold the complete logo
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Exit animation
      await controls.start("exit");
      
      // Complete the splash screen
      setTimeout(() => {
        setIsVisible(false);
        onComplete();
      }, 800);
    };

    animateSequence();
  }, [controls, onComplete, isMounted]);

  if (!isVisible) return null;

  return (
    <motion.div 
      className="fixed inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 z-50 flex items-center justify-center"
      initial={{ opacity: 1 }}
      animate={controls}
      variants={{
        exit: { opacity: 0 }
      }}
    >
      <div className="text-center">
        <motion.svg
          ref={svgRef}
          xmlns="http://www.w3.org/2000/svg"
          xmlnsXlink="http://www.w3.org/1999/xlink"
          width="375"
          height="375"
          viewBox="0 0 375 374.999991"
          preserveAspectRatio="xMidYMid meet"
          version="1.0"
          className="mx-auto mb-4 w-auto max-w-xs md:max-w-sm lg:max-w-md"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={controls}
          variants={{
            visible: { opacity: 1, scale: 1 },
            exit: { opacity: 0, scale: 0.85 }
          }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <defs><g/></defs>
          <g fill="#f6f6f6" fillOpacity="1">
            <g transform="translate(70.451169, 197.30212)">
              <g>
                <path d="M 21.296875 -14.609375 C 21.648438 -14.535156 21.914062 -14.359375 22.09375 -14.078125 C 22.28125 -13.804688 22.335938 -13.507812 22.265625 -13.1875 C 21.648438 -10.1875 21.070312 -7.800781 20.53125 -6.03125 C 20 -4.257812 19.503906 -3.015625 19.046875 -2.296875 C 18.066406 -0.734375 16.941406 0.144531 15.671875 0.34375 C 15.546875 0.375 15.410156 0.40625 15.265625 0.4375 C 15.117188 0.46875 14.960938 0.5 14.796875 0.53125 C 14.117188 0.570312 13.492188 0.46875 12.921875 0.21875 C 12.347656 -0.0195312 11.832031 -0.421875 11.375 -0.984375 C 10.757812 -1.828125 10.320312 -2.71875 10.0625 -3.65625 C 9.800781 -4.601562 9.601562 -5.566406 9.46875 -6.546875 C 9.4375 -7.097656 9.378906 -7.785156 9.296875 -8.609375 C 9.222656 -9.441406 9.144531 -10.179688 9.0625 -10.828125 C 8.976562 -11.484375 8.921875 -11.8125 8.890625 -11.8125 C 8.753906 -11.394531 8.628906 -10.972656 8.515625 -10.546875 C 8.410156 -10.128906 8.289062 -9.691406 8.15625 -9.234375 C 7.988281 -8.546875 7.832031 -7.859375 7.6875 -7.171875 C 7.539062 -6.492188 7.375 -5.828125 7.1875 -5.171875 C 7.082031 -4.847656 6.914062 -4.394531 6.6875 -3.8125 C 6.457031 -3.226562 6.210938 -2.617188 5.953125 -1.984375 C 5.691406 -1.347656 5.445312 -0.773438 5.21875 -0.265625 C 5 0.234375 4.835938 0.566406 4.734375 0.734375 C 4.410156 1.160156 4.003906 1.425781 3.515625 1.53125 C 3.023438 1.644531 2.519531 1.59375 2 1.375 C 1.09375 0.945312 0.453125 0.367188 0.078125 -0.359375 C -0.296875 -1.097656 -0.519531 -1.90625 -0.59375 -2.78125 C -0.6875 -4.507812 -0.6875 -6.21875 -0.59375 -7.90625 C -0.519531 -9.175781 -0.34375 -10.429688 -0.0625 -11.671875 C 0.207031 -12.910156 0.488281 -14.148438 0.78125 -15.390625 C 1.039062 -16.429688 1.378906 -17.445312 1.796875 -18.4375 C 2.222656 -19.425781 2.679688 -20.378906 3.171875 -21.296875 C 3.628906 -22.078125 4.207031 -22.726562 4.90625 -23.25 C 5.601562 -23.769531 6.441406 -24.09375 7.421875 -24.21875 C 7.679688 -24.257812 7.929688 -24.285156 8.171875 -24.296875 C 8.421875 -24.316406 8.675781 -24.34375 8.9375 -24.375 C 9.226562 -24.40625 9.4375 -24.59375 9.5625 -24.9375 C 9.695312 -25.28125 9.78125 -25.515625 9.8125 -25.640625 C 9.976562 -26.097656 10.207031 -26.53125 10.5 -26.9375 C 10.789062 -27.34375 11.132812 -27.660156 11.53125 -27.890625 C 11.820312 -28.054688 12.226562 -28.15625 12.75 -28.1875 C 13.269531 -28.21875 13.753906 -28.222656 14.203125 -28.203125 C 14.660156 -28.191406 14.910156 -28.1875 14.953125 -28.1875 C 15.828125 -28.28125 16.347656 -27.921875 16.515625 -27.109375 C 16.578125 -26.847656 16.59375 -26.367188 16.5625 -25.671875 C 16.53125 -24.972656 16.476562 -24.28125 16.40625 -23.59375 C 16.34375 -22.90625 16.296875 -22.414062 16.265625 -22.125 C 16.234375 -21.925781 16.164062 -21.40625 16.0625 -20.5625 C 15.96875 -19.71875 15.835938 -18.707031 15.671875 -17.53125 C 15.515625 -16.363281 15.351562 -15.191406 15.1875 -14.015625 C 15.03125 -12.847656 14.898438 -11.804688 14.796875 -10.890625 C 14.703125 -9.984375 14.640625 -9.378906 14.609375 -9.078125 C 14.609375 -8.890625 14.582031 -8.441406 14.53125 -7.734375 C 14.476562 -7.035156 14.457031 -6.265625 14.46875 -5.421875 C 14.488281 -4.578125 14.5625 -3.820312 14.6875 -3.15625 C 14.820312 -2.488281 15.039062 -2.101562 15.34375 -2 C 15.789062 -2.0625 16.328125 -2.566406 16.953125 -3.515625 C 17.703125 -4.722656 18.660156 -8.109375 19.828125 -13.671875 C 19.890625 -14.003906 20.066406 -14.257812 20.359375 -14.4375 C 20.660156 -14.613281 20.972656 -14.671875 21.296875 -14.609375 Z M 2.828125 -7.46875 C 3.023438 -7.34375 3.3125 -7.597656 3.6875 -8.234375 C 4.0625 -8.867188 4.484375 -9.742188 4.953125 -10.859375 C 5.429688 -11.984375 5.910156 -13.226562 6.390625 -14.59375 C 6.878906 -15.96875 7.335938 -17.320312 7.765625 -18.65625 C 8.191406 -19.988281 8.535156 -21.160156 8.796875 -22.171875 C 7.585938 -22.691406 6.625 -22.515625 5.90625 -21.640625 C 5.488281 -21.117188 5.15625 -20.554688 4.90625 -19.953125 C 4.664062 -19.347656 4.398438 -18.769531 4.109375 -18.21875 C 3.941406 -17.863281 3.757812 -17.375 3.5625 -16.75 C 3.363281 -16.132812 3.195312 -15.570312 3.0625 -15.0625 C 2.9375 -14.5625 2.859375 -14.265625 2.828125 -14.171875 L 2.828125 -14.109375 C 2.828125 -14.109375 2.769531 -13.816406 2.65625 -13.234375 C 2.539062 -12.648438 2.425781 -11.953125 2.3125 -11.140625 C 2.207031 -10.328125 2.1875 -9.566406 2.25 -8.859375 C 2.3125 -8.160156 2.503906 -7.695312 2.828125 -7.46875 Z M 2.828125 -7.46875 " />
              </g>
            </g>
          </g>
          <g fill="#f6f6f6" fillOpacity="1">
            <g transform="translate(90.228172, 197.30212)">
              <g>
                <path d="M 28.8125 -14.65625 C 29.175781 -14.65625 29.46875 -14.539062 29.6875 -14.3125 C 29.914062 -14.082031 30.015625 -13.789062 29.984375 -13.4375 C 29.984375 -13.101562 29.859375 -12.820312 29.609375 -12.59375 C 29.367188 -12.375 29.070312 -12.28125 28.71875 -12.3125 C 27.613281 -12.375 26.554688 -12.304688 25.546875 -12.109375 C 24.535156 -11.910156 23.554688 -11.632812 22.609375 -11.28125 L 22.5625 -10.453125 C 22.46875 -9.640625 22.367188 -8.804688 22.265625 -7.953125 C 22.171875 -7.109375 22.039062 -6.265625 21.875 -5.421875 C 21.65625 -4.023438 21.414062 -2.609375 21.15625 -1.171875 C 20.894531 0.265625 20.46875 1.664062 19.875 3.03125 C 19.644531 3.582031 19.367188 4.109375 19.046875 4.609375 C 18.722656 5.117188 18.300781 5.566406 17.78125 5.953125 C 17.550781 6.117188 17.3125 6.207031 17.0625 6.21875 C 16.820312 6.238281 16.570312 6.234375 16.3125 6.203125 C 15.695312 6.066406 15.191406 5.914062 14.796875 5.75 C 14.410156 5.59375 14.179688 5.5 14.109375 5.46875 C 12.847656 4.71875 12.007812 3.578125 11.59375 2.046875 C 11.1875 0.515625 11.296875 -1.207031 11.921875 -3.125 C 12.378906 -4.59375 13.09375 -6.015625 14.0625 -7.390625 C 15.039062 -8.773438 16.226562 -10.023438 17.625 -11.140625 C 17.757812 -11.265625 17.84375 -11.359375 17.875 -11.421875 C 18.164062 -12.597656 18.394531 -13.617188 18.5625 -14.484375 C 18.726562 -15.347656 18.84375 -16.003906 18.90625 -16.453125 C 18.9375 -16.585938 18.957031 -16.75 18.96875 -16.9375 C 18.988281 -17.132812 19 -17.234375 19 -17.234375 C 19 -17.535156 18.984375 -17.832031 18.953125 -18.125 C 18.921875 -18.414062 18.820312 -18.59375 18.65625 -18.65625 C 18.394531 -18.820312 18.082031 -18.804688 17.71875 -18.609375 C 17.363281 -18.410156 17.015625 -18.15625 16.671875 -17.84375 C 16.335938 -17.539062 16.054688 -17.289062 15.828125 -17.09375 C 15.628906 -16.925781 15.445312 -16.742188 15.28125 -16.546875 C 15.125 -16.359375 14.960938 -16.164062 14.796875 -15.96875 C 14.566406 -15.707031 14.347656 -15.429688 14.140625 -15.140625 C 13.929688 -14.847656 13.710938 -14.570312 13.484375 -14.3125 C 13.160156 -13.914062 12.828125 -13.515625 12.484375 -13.109375 C 12.140625 -12.703125 11.785156 -12.289062 11.421875 -11.875 C 11.128906 -11.507812 10.835938 -11.128906 10.546875 -10.734375 C 10.253906 -10.347656 9.960938 -9.960938 9.671875 -9.578125 C 9.671875 -9.578125 9.554688 -9.445312 9.328125 -9.1875 C 9.097656 -8.925781 8.851562 -8.566406 8.59375 -8.109375 C 8.5625 -6.316406 8.5625 -4.382812 8.59375 -2.3125 C 8.625 -0.25 8.738281 1.820312 8.9375 3.90625 C 9.03125 4.5625 9.117188 5.195312 9.203125 5.8125 C 9.285156 6.425781 9.34375 7.015625 9.375 7.578125 C 9.507812 8.316406 9.582031 8.960938 9.59375 9.515625 C 9.613281 10.078125 9.523438 10.472656 9.328125 10.703125 C 8.898438 11.148438 8.457031 11.4375 8 11.5625 C 7.550781 11.695312 7.195312 11.765625 6.9375 11.765625 C 6.707031 11.734375 6.46875 11.691406 6.21875 11.640625 C 5.976562 11.597656 5.742188 11.5625 5.515625 11.53125 C 4.835938 11.363281 4.375 11.019531 4.125 10.5 C 3.882812 9.976562 3.632812 9.472656 3.375 8.984375 C 3.039062 8.265625 2.734375 7.539062 2.453125 6.8125 C 2.179688 6.082031 1.929688 5.34375 1.703125 4.59375 C 1.484375 3.78125 1.296875 2.945312 1.140625 2.09375 C 0.992188 1.25 0.84375 0.421875 0.6875 -0.390625 C 0.625 -0.847656 0.570312 -1.3125 0.53125 -1.78125 C 0.5 -2.25 0.46875 -2.726562 0.4375 -3.21875 C 0.4375 -3.21875 0.394531 -3.707031 0.3125 -4.6875 C 0.238281 -5.664062 0.175781 -7.023438 0.125 -8.765625 C 0.0703125 -10.503906 0.078125 -12.484375 0.140625 -14.703125 C 0.179688 -14.992188 0.21875 -15.285156 0.25 -15.578125 C 0.28125 -15.867188 0.3125 -16.160156 0.34375 -16.453125 L 0.296875 -16.453125 C 0.554688 -20.234375 1.132812 -23.414062 2.03125 -26 C 2.925781 -28.59375 3.941406 -30.710938 5.078125 -32.359375 C 6.222656 -34.003906 7.347656 -35.273438 8.453125 -36.171875 C 9.554688 -37.066406 10.46875 -37.679688 11.1875 -38.015625 C 11.90625 -38.359375 12.265625 -38.53125 12.265625 -38.53125 C 13.109375 -38.863281 13.925781 -38.859375 14.71875 -38.515625 C 15.519531 -38.171875 16.097656 -37.753906 16.453125 -37.265625 C 16.710938 -36.910156 16.890625 -36.503906 16.984375 -36.046875 C 17.085938 -35.585938 17.15625 -35.128906 17.1875 -34.671875 C 17.320312 -33.765625 17.347656 -32.867188 17.265625 -31.984375 C 17.179688 -31.109375 17.0625 -30.234375 16.90625 -29.359375 C 16.738281 -28.546875 16.566406 -27.726562 16.390625 -26.90625 C 16.210938 -26.09375 16.007812 -25.28125 15.78125 -24.46875 C 15.707031 -24.175781 15.570312 -23.800781 15.375 -23.34375 C 15.1875 -22.882812 15.019531 -22.46875 14.875 -22.09375 C 14.726562 -21.726562 14.65625 -21.546875 14.65625 -21.546875 C 15.175781 -21.703125 15.6875 -21.859375 16.1875 -22.015625 C 16.695312 -22.179688 17.210938 -22.332031 17.734375 -22.46875 C 18.609375 -22.664062 19.441406 -22.597656 20.234375 -22.265625 C 21.035156 -21.941406 21.632812 -21.375 22.03125 -20.5625 C 22.09375 -20.4375 22.269531 -19.8125 22.5625 -18.6875 C 22.851562 -17.5625 22.9375 -16.101562 22.8125 -14.3125 L 22.8125 -13.875 C 23.71875 -14.164062 24.675781 -14.382812 25.6875 -14.53125 C 26.695312 -14.675781 27.738281 -14.71875 28.8125 -14.65625 Z M 12.703125 -23.828125 C 13.316406 -25.066406 13.890625 -26.3125 14.421875 -27.5625 C 14.960938 -28.820312 15.363281 -30.117188 15.625 -31.453125 C 15.789062 -32.203125 15.890625 -32.957031 15.921875 -33.71875 C 15.953125 -34.488281 15.890625 -35.234375 15.734375 -35.953125 C 15.691406 -36.210938 15.613281 -36.460938 15.5 -36.703125 C 15.394531 -36.953125 15.242188 -37.140625 15.046875 -37.265625 C 14.722656 -37.523438 14.375 -37.425781 14 -36.96875 C 13.625 -36.519531 13.269531 -35.90625 12.9375 -35.125 C 12.613281 -34.34375 12.3125 -33.566406 12.03125 -32.796875 C 11.757812 -32.035156 11.539062 -31.472656 11.375 -31.109375 C 11.25 -30.785156 11.078125 -30.234375 10.859375 -29.453125 C 10.648438 -28.671875 10.441406 -27.847656 10.234375 -26.984375 C 10.023438 -26.117188 9.84375 -25.351562 9.6875 -24.6875 C 9.539062 -24.019531 9.46875 -23.65625 9.46875 -23.59375 C 9.4375 -23.101562 9.394531 -22.613281 9.34375 -22.125 C 9.300781 -21.632812 9.265625 -21.144531 9.234375 -20.65625 C 9.234375 -20.070312 9.234375 -19.492188 9.234375 -18.921875 C 9.234375 -18.359375 9.234375 -17.765625 9.234375 -17.140625 C 9.359375 -17.398438 9.46875 -17.644531 9.5625 -17.875 C 9.664062 -18.101562 9.785156 -18.332031 9.921875 -18.5625 C 10.367188 -19.4375 10.835938 -20.304688 11.328125 -21.171875 C 11.816406 -22.035156 12.273438 -22.921875 12.703125 -23.828125 Z M 14.75 2.9375 C 15.101562 1.269531 15.457031 -0.398438 15.8125 -2.078125 C 16.175781 -3.753906 16.519531 -5.421875 16.84375 -7.078125 C 16.257812 -6.359375 15.75 -5.609375 15.3125 -4.828125 C 14.875 -4.046875 14.523438 -3.25 14.265625 -2.4375 C 13.515625 0 13.675781 1.789062 14.75 2.9375 Z M 14.75 2.9375 " />
              </g>
            </g>
          </g>
          {/* Remaining path groups abbreviated for brevity */}
        </motion.svg>
        
        <motion.div
          className="w-32 h-0.5 bg-gradient-to-r from-transparent via-accent to-transparent mx-auto"
          initial={{ scaleX: 0, opacity: 0 }}
          animate={controls}
          variants={{
            visible: {
              scaleX: 1,
              opacity: 1,
            }
          }}
          transition={{
            duration: 1.2,
            delay: 2.5,
            ease: "easeInOut"
          }}
        />
        
        <motion.p
          className="text-sm md:text-base text-foreground/70 mt-4 font-light tracking-widest"
          initial={{ opacity: 0, y: 20 }}
          animate={controls}
          variants={{
            visible: {
              opacity: 1,
              y: 0,
            }
          }}
          transition={{
            duration: 0.8,
            delay: 3,
            ease: "easeOut"
          }}
        >
          {t('splashSubtitle')}
        </motion.p>
      </div>
    </motion.div>
  );
}
