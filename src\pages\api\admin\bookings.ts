import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_ROLE = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE) {
      console.error('Supabase env vars are missing')
      return res.status(500).json({ error: 'Supabase not configured' })
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE)

    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' })
    }

    const { data, error } = await supabase
      .from('bookings')
      .select('id,date,time,name,surname,email,phone,status,visit_type,message,created_at')
      .order('date', { ascending: false })
      .order('time', { ascending: false })

    if (error) {
      console.error('Supabase select bookings error', error)
      return res.status(500).json({ error: 'Failed to fetch bookings' })
    }

    return res.status(200).json(Array.isArray(data) ? data : [])
  } catch (err) {
    console.error('Unexpected admin bookings error', err)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
