import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { buildConfirmationSMS } from '@/lib/notifications';
import { sendSMSViaTwilio } from '@/lib/twilio';

// Local helper to format time as HH:MM (strip seconds if present)
function formatShortTime(timeStr?: string): string {
  if (!timeStr) return '';
  const trimmed = String(timeStr).trim();
  const m = trimmed.match(/^(\d{1,2}:\d{2})/);
  if (m) return m[1].padStart(5, '0');
  return trimmed;
}

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE;


type BookingPayload = {
  name: string;
  surname?: string | null;
  email?: string | null;
  phone: string;
  visitType: string;
  date: string;
  time: string;
  slot_id?: number | null;
  message?: string | null;
  language?: string;
};

// DB row types
interface Slot {
  id: number;
  capacity?: number | null;
  start_time?: string | null;
  date?: string | null;
}

interface BookingRow {
  id?: number;
  name: string;
  surname?: string | null;
  email?: string | null;
  phone: string;
  visit_type?: string | null;
  date: string;
  time: string;
  slot_id?: number | null;
  message?: string | null;
  language?: string | null;
  status?: string | null;
  confirmation_retry_count?: number | null;
  reminder_retry_count?: number | null;
  confirmation_sent?: boolean | null;
  reminder_sent?: boolean | null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE) {
      console.error('Supabase env vars are missing: SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
      return res.status(500).json({ error: 'Supabase is not configured on the server' });
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE);

    if (req.method === 'POST') {
      const payload = req.body as BookingPayload;

      // minimal server-side validation
      if (!payload.name || !payload.phone || !payload.date || !payload.time || !payload.visitType) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      // Дополнительная валидация номера телефона
      // Убираем пробелы для подсчёта реальной длины
      const phoneForValidation = payload.phone.replace(/\s/g, '');
      
      if (!payload.phone || phoneForValidation.length < 7) {
        console.error('Phone number too short:', payload.phone, 'cleaned length:', phoneForValidation.length);
        return res.status(400).json({ error: 'Номер телефона слишком короткий. Введите полный номер.' });
      }

      // Проверяем формат (разрешаем пробелы)
      const phoneRegex = /^\+?[\d\s\-\(\)\.]{7,25}$/; // Увеличили лимит до 25 для пробелов
      if (!phoneRegex.test(payload.phone)) {
        console.error('Invalid phone format received:', payload.phone);
        console.error('Phone length:', payload.phone.length);
        console.error('Phone without spaces:', phoneForValidation.length);
  return res.status(400).json({ error: 'Неверный формат номера. Пример правильного формата: +371 26094018' });
      }

      console.log('📋 Booking request:', {
        name: payload.name,
        phone: payload.phone,
        date: payload.date,
        time: payload.time,
        visitType: payload.visitType,
        language: payload.language
      });

      // Try to resolve a slot_id: either provided or find by date+time
      let slotIdToUse: number | null = null;

      if (payload.slot_id) {
        slotIdToUse = payload.slot_id;
      } else {
        // Find a slot that matches date and start_time == time
        const slotRes = await supabase
          .from('slots')
          .select('id,capacity')
          .eq('date', payload.date)
          .eq('start_time', payload.time)
          .limit(1)
          .maybeSingle();

        if (slotRes.error) {
          console.error('Supabase select slot error', slotRes.error);
          return res.status(409).json({ error: 'Requested time slot is not available' });
        }

        const slotData = slotRes.data as Slot | null;
        if (!slotData) {
          return res.status(409).json({ error: 'Requested time slot is not available' });
        }
        slotIdToUse = slotData.id;
      }

      // If slot resolved, check capacity
      if (slotIdToUse) {
        // count existing active bookings for this slot
        const { count, error: countErr } = await supabase
          .from('bookings')
          .select('id', { count: 'exact', head: true })
          .eq('slot_id', slotIdToUse)
          .eq('status', 'booked');

        if (countErr) {
          console.error('Supabase count error', countErr);
          return res.status(500).json({ error: 'Server error checking slot availability' });
        }

        // get slot capacity
        const slotInfo = await supabase.from('slots').select('capacity').eq('id', slotIdToUse).maybeSingle();
        if (slotInfo.error) {
          console.error('Supabase slot info error', slotInfo.error);
          return res.status(500).json({ error: 'Server error' });
        }
        const slotInfoData = slotInfo.data as Slot | null;
        const capacity = slotInfoData?.capacity ?? 1;
        const existing = typeof count === 'number' ? count : 0;

        if (existing >= capacity) {
          return res.status(409).json({ error: 'Slot is already full' });
        }
      }

      // Insert booking linked to slotIdToUse
  const { data, error } = await supabase.from('bookings').insert([
        {
          name: payload.name,
          surname: payload.surname || null,
          email: payload.email || null,
          phone: payload.phone,
          visit_type: payload.visitType,
          date: payload.date,
          time: payload.time,
          slot_id: slotIdToUse,
          message: payload.message || null,
          language: payload.language || 'ru',
          status: 'booked',
          created_at: new Date().toISOString(),
        },
      ]);

      if (error) {
        console.error('Supabase insert error', error);
        return res.status(500).json({ error: error.message });
      }

      // Send SMS confirmation via Twilio
      try {
        console.log('📤 Preparing to send SMS confirmation...');
        
        // Build message using centralized templates
  const body = buildConfirmationSMS({
          name: payload.name,
          date: payload.date,
          time: formatShortTime(payload.time),
          address: process.env.BUSINESS_ADDRESS || undefined,
          language: (payload.language as 'ru' | 'lv') || 'ru',
        });

        console.log('📱 SMS details:', {
          to: payload.phone,
          bodyLength: body.length,
          language: payload.language
        });

  const smsSuccess = await sendSMSViaTwilio({ to: payload.phone, text: body });

        if (smsSuccess) {
          // Mark confirmation sent
          try {
            const inserted: BookingRow | undefined = Array.isArray(data) ? (data as unknown as BookingRow[])[0] : (data as unknown as BookingRow | undefined);
            if (inserted && inserted.id) {
              await supabase.from('bookings').update({ confirmation_sent: true }).eq('id', inserted.id);
            }
          } catch (markErr: unknown) {
            console.error('Failed to mark confirmation_sent', markErr);
          }
        }
      } catch (smsErr: unknown) {
        // Log SMS errors and increment retry counter
        const smsErrMessage = smsErr instanceof Error ? smsErr.message : String(smsErr);
        console.error('Failed to send SMS confirmation', smsErrMessage);
        try {
            const inserted: BookingRow | undefined = Array.isArray(data) ? (data as unknown as BookingRow[])[0] : (data as unknown as BookingRow | undefined);
          if (inserted && inserted.id) {
            await supabase.from('bookings').update({
              confirmation_retry_count: (inserted.confirmation_retry_count || 0) + 1,
              last_confirmation_error: smsErrMessage
            }).eq('id', inserted.id);
          }
        } catch (uErr: unknown) {
          console.error('Failed to update retry count for booking', uErr);
        }
      }

      return res.status(200).json({ data });
    }

    if (req.method === 'GET') {
      const { date, month } = req.query;
      // If a single date is requested, return times for that date
      if (date && typeof date === 'string') {
        // Get booked times
        const { data: bookingsData, error: bookingsError } = await supabase
          .from('bookings')
          .select('time')
          .eq('date', date)
          .eq('status', 'booked');

        if (bookingsError) {
          console.error('Supabase select bookings error', bookingsError);
          return res.status(500).json({ error: bookingsError.message });
        }

        // Get available slots for this date
        const { data: slotsData, error: slotsError } = await supabase
          .from('slots')
          .select('start_time, capacity')
          .eq('date', date);

        if (slotsError) {
          console.error('Supabase select slots error', slotsError);
          return res.status(500).json({ error: slotsError.message });
        }

  const bookedTimes = Array.isArray(bookingsData) ? (bookingsData as unknown as { time: string }[]).map((r) => r.time) : [];
  const allSlots: Slot[] = Array.isArray(slotsData) ? (slotsData as unknown as Slot[]) : [];
        
        // Calculate available times (slots that aren't fully booked)
        const availableTimes: string[] = [];
        allSlots.forEach((slot) => {
          const capacity = slot.capacity || 1;
          const bookedCount = bookedTimes.filter(time => time === slot.start_time).length;
          if (bookedCount < capacity) {
            if (slot.start_time) availableTimes.push(slot.start_time);
          }
        });

        return res.status(200).json({ 
          times: bookedTimes, 
          availableTimes: availableTimes.sort() 
        });
      }

      // If month=YYYY-MM is requested, return counts per date for that month
      if (month && typeof month === 'string') {
        // derive start and end of month
        const [y, m] = month.split('-').map(Number);
        if (!y || !m) return res.status(400).json({ error: 'Invalid month parameter' });
        const startDate = `${month}-01`;
        const endMonth = m === 12 ? `${y + 1}-01-01` : `${y}-${String(m + 1).padStart(2, '0')}-01`;

        const { data, error } = await supabase
          .from('bookings')
          .select('date,time')
          .eq('status', 'booked') // Только активные бронирования
          .gte('date', startDate)
          .lt('date', endMonth);

        if (error) {
          console.error('Supabase select error', error);
          return res.status(500).json({ error: error.message });
        }

        const counts: Record<string, number> = {};
        (data || []).forEach((r) => {
          const d = (r as unknown as { date: string }).date;
          counts[d] = (counts[d] || 0) + 1;
        });

        return res.status(200).json({ counts });
      }

      return res.status(400).json({ error: 'Missing date or month query parameter' });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (err: unknown) {
    const message = err instanceof Error ? err.message : String(err);
    console.error('API error', message);
    return res.status(500).json({ error: message || 'Unknown error' });
  }
}
