"use client";

import Image from 'next/image'
import { motion } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';
import { Instagram, Facebook } from 'lucide-react';

export default function AboutSection() {
  const { t } = useLanguage();

  // Achievements removed — replaced with Follow-us CTA

  return (
    <section id="about" className="py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#e6e6fa1a' }}>
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-block px-4 py-2 bg-accent/10 border border-accent/20 rounded-full text-accent text-sm font-medium mb-6"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              {t('about')}
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-serif text-foreground mb-6 leading-tight">
              {t('aboutShortTitle')}
              <span className="block text-accent">{t('withExperience')}</span>
            </h2>

            <p className="text-lg text-foreground/80 mb-8 leading-relaxed">
              {t('aboutText')}
            </p>

            {/* Follow-us CTA */}
            <motion.div
              className="text-center p-6 bg-background/50 rounded-2xl border border-border/50 backdrop-blur-sm mb-8"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <p className="text-sm text-foreground/70 mb-4">{t('followUsDesc')}</p>

              <div className="flex items-center justify-center space-x-4">
                <motion.a
                  href="https://www.instagram.com/__aboutyourskin?igsh=MXR3bWVmbmlkNWExOA=="
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-accent/10 border border-accent/20 rounded-full flex items-center justify-center text-accent hover:bg-accent hover:text-background transition-all duration-300"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Instagram className="w-5 h-5" />
                </motion.a>

                <motion.a
                  href="https://www.facebook.com/share/16C44uRfQE/?mibextid=wwXIfr"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-accent/10 border border-accent/20 rounded-full flex items-center justify-center text-accent hover:bg-accent hover:text-background transition-all duration-300"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Facebook className="w-5 h-5" />
                </motion.a>
              </div>
            </motion.div>

            <motion.button
              className="bg-accent/70 text-background px-8 py-4 rounded-full font-medium hover:bg-accent/60 transition-all duration-300 shadow-lg hover:shadow-accent/5"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => document.querySelector('#services')?.scrollIntoView({ behavior: 'smooth' })}
            >
              {t('viewServices')}
            </motion.button>
          </motion.div>

          {/* Image/Visual Content */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="relative">
              {/* Main image placeholder */}
              <div className="aspect-[4/5] bg-gradient-to-br from-accent/20 to-secondary/30 rounded-3xl overflow-hidden border border-accent/20 relative">
                <Image
                  src="/proffesionalphoto.jpg"
                  alt="Profesionāla foto"
                  fill
                  sizes="(max-width: 768px) 90vw, 40vw"
                  className="object-cover rounded-3xl"
                />
              </div>

              {/* ...floating elements removed... */}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
