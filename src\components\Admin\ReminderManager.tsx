import { useState } from 'react';

interface ReminderManagerProps {
  onClose?: () => void;
}

interface ReminderResult {
  message: string;
  total: number;
  sent: number;
  failed: number;
}

export default function ReminderManager({ onClose }: ReminderManagerProps) {
  const [sending, setSending] = useState(false);
  const [result, setResult] = useState<ReminderResult | null>(null);
  const [error, setError] = useState('');

  const sendReminders = async () => {
    setSending(true);
    setError('');
    setResult(null);
    
    try {
      console.log('📤 Triggering reminder sending...');
      
      const response = await fetch('/api/send-reminders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
        console.log('✅ Reminders sent:', data);
      } else {
        setError(data.error || 'Ошибка отправки напоминаний');
        console.error('❌ Reminder error:', data);
      }

    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Ошибка сети';
      setError(errorMessage);
      console.error('💥 Network error:', err);
    } finally {
      setSending(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-900 p-8 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white">SMS Напоминания</h2>
          {onClose && (
            <button 
              onClick={onClose}
              className="text-gray-400 hover:text-white text-2xl"
            >
              ×
            </button>
          )}
        </div>

        <div className="space-y-4">
          <p className="text-gray-300 text-sm">
            Отправка SMS-напоминаний всем клиентам с бронированием на завтра
          </p>

          <button
            onClick={sendReminders}
            disabled={sending}
            className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
              sending 
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {sending ? '📤 Отправляем...' : '🔔 Отправить напоминания'}
          </button>

          {error && (
            <div className="p-3 bg-red-900/50 border border-red-500 rounded-lg">
              <p className="text-red-300 text-sm">❌ {error}</p>
            </div>
          )}

          {result && (
            <div className="p-4 bg-green-900/50 border border-green-500 rounded-lg">
              <h3 className="text-green-300 font-medium mb-2">✅ Результат отправки:</h3>
              <div className="text-sm text-green-200 space-y-1">
                <p>📊 Всего бронирований: {result.total}</p>
                <p>✅ Отправлено: {result.sent}</p>
                <p>❌ Ошибок: {result.failed}</p>
              </div>
            </div>
          )}

          <div className="pt-4 border-t border-gray-700">
            <h3 className="text-white font-medium mb-2">Автоматизация</h3>
            <p className="text-gray-400 text-xs">
              Для автоматической отправки настройте cron job на:<br/>
              <code className="bg-gray-800 px-2 py-1 rounded text-green-400">
                GET /api/cron/daily-reminders
              </code>
            </p>
            <p className="text-gray-400 text-xs mt-2">
              Рекомендуется запускать каждый день в 10:00 утра
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
