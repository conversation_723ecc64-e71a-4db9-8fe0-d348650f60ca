// Twilio SMS integration
import twilio from 'twilio';

export interface TwilioSMSParams {
  to: string // Номер получателя в международном формате (+4917644545715)
  text: string // Текст сообщения
}

// Функция для очистки и валидации номера телефона
function cleanPhoneNumber(phone: string): string | null {
  if (!phone) return null;
  
  console.log('🧹 Cleaning phone number:', phone);
  
  // Убираем все пробелы, дефисы, скобки, точки, но оставляем +
  let cleaned = phone.replace(/[\s\-\(\)\.]/g, '');
  
  // Если номер начинается с 8, заменяем на 7 (для России)
  if (cleaned.startsWith('8') && cleaned.length === 11) {
    cleaned = '7' + cleaned.substring(1);
  }
  
  // Добавляем + если его нет
  if (!cleaned.startsWith('+')) {
    cleaned = '+' + cleaned;
  }
  
  console.log('🔍 Cleaned phone:', cleaned);
  
  // Проверяем, что номер содержит минимум 7 цифр после + (учитывая короткие номера)
  const phoneRegex = /^\+\d{7,15}$/;
  if (!phoneRegex.test(cleaned)) {
    console.log('❌ Phone validation failed:', {
      phone: cleaned,
      totalLength: cleaned.length,
      digitsAfterPlus: cleaned.replace(/\+/, '').length,
      pattern: 'Expected format: +XXXXXXXXXXX (7-15 digits after +)'
    });
    return null;
  }
  
  console.log('✅ Phone validation passed:', cleaned);
  return cleaned;
}

export async function sendSMSViaTwilio({ to, text }: TwilioSMSParams): Promise<boolean> {
  try {
    if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN || !process.env.TWILIO_PHONE_NUMBER) {
      console.error('Twilio credentials are not configured')
      console.error('Missing:', {
        TWILIO_ACCOUNT_SID: !!process.env.TWILIO_ACCOUNT_SID,
        TWILIO_AUTH_TOKEN: !!process.env.TWILIO_AUTH_TOKEN,
        TWILIO_PHONE_NUMBER: !!process.env.TWILIO_PHONE_NUMBER
      })
      return false
    }

    // Очищаем и валидируем номер телефона
    const cleanedPhone = cleanPhoneNumber(to);
    if (!cleanedPhone) {
      console.error('❌ Invalid phone number format:', to);
      return false;
    }

    console.log('📱 Original phone:', to);
    console.log('🧹 Cleaned phone:', cleanedPhone);
    console.log('💬 Message:', text)

    // Инициализация Twilio клиента
    const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN)

    // Отправка SMS
    const message = await client.messages.create({
      body: text,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: cleanedPhone // Используем очищенный номер
    })

    console.log('✅ SMS sent successfully via Twilio:', message.sid)
    console.log('Message status:', message.status)
    
    return true

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('❌ Twilio SMS sending failed:', errorMessage)
    console.error('Error details:', error)
    return false
  }
}
